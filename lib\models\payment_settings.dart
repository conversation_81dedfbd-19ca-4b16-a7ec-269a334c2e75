import 'package:hive/hive.dart';
import 'student_payment.dart';

part 'payment_settings.g.dart';

/// إعدادات نظام المدفوعات المتقدم
@HiveType(typeId: 26)
class PaymentSettings extends HiveObject {
  @HiveField(0)
  PaymentCalculationMethod calculationMethod;

  @HiveField(1)
  int sessionsCount; // عدد الحصص للطريقة الأولى

  @HiveField(2)
  double defaultAmount; // المبلغ الافتراضي

  @HiveField(3)
  ArchiveFrequency archiveFrequency; // تكرار الأرشفة

  @HiveField(4)
  bool autoArchiveEnabled; // تفعيل الأرشفة التلقائية

  @HiveField(5)
  DateTime lastArchiveDate; // تاريخ آخر أرشفة

  @HiveField(6)
  bool integrateWithLessons; // التكامل مع نظام الدروس

  @HiveField(7)
  int overdueGraceDays; // أيام السماح قبل اعتبار الطالب متأخر

  @HiveField(8)
  DateTime createdAt;

  @HiveField(9)
  OverdueCalculationMethod overdueCalculationMethod;

  PaymentSettings({
    this.calculationMethod = PaymentCalculationMethod.monthly,
    this.sessionsCount = 8,
    this.defaultAmount = 100.0,
    this.archiveFrequency = ArchiveFrequency.monthly,
    this.autoArchiveEnabled = true,
    DateTime? lastArchiveDate,
    this.integrateWithLessons = true,
    this.overdueGraceDays = 3,
    this.overdueCalculationMethod = OverdueCalculationMethod.monthEnd,
    required this.createdAt,
  }) : lastArchiveDate = lastArchiveDate ?? DateTime.now();

  /// إنشاء إعدادات افتراضية
  factory PaymentSettings.defaultSettings() {
    return PaymentSettings(createdAt: DateTime.now());
  }

  /// هل حان وقت الأرشفة؟
  bool get shouldArchive {
    if (!autoArchiveEnabled) return false;

    final now = DateTime.now();
    switch (archiveFrequency) {
      case ArchiveFrequency.daily:
        return now.difference(lastArchiveDate).inDays >= 1;
      case ArchiveFrequency.weekly:
        return now.difference(lastArchiveDate).inDays >= 7;
      case ArchiveFrequency.monthly:
        return now.month != lastArchiveDate.month ||
            now.year != lastArchiveDate.year;
      case ArchiveFrequency.yearly:
        return now.year != lastArchiveDate.year;
    }
  }

  /// تحديث تاريخ آخر أرشفة
  void updateLastArchiveDate() {
    lastArchiveDate = DateTime.now();
  }
}

/// نظام العمل (شهري فقط)
@HiveType(typeId: 27)
enum PaymentCalculationMethod {
  @HiveField(1)
  monthly, // النظام الشهري فقط
}

/// طريقة احتساب التأخير
@HiveType(typeId: 29)
enum OverdueCalculationMethod {
  @HiveField(0)
  monthStart, // عند بداية الشهر

  @HiveField(1)
  monthEnd, // عند انتهاء الشهر

  @HiveField(2)
  sessionsCount, // عند إكمال عدد محدد من الحصص
}

/// تكرار الأرشفة
@HiveType(typeId: 28)
enum ArchiveFrequency {
  @HiveField(0)
  daily, // يومياً

  @HiveField(1)
  weekly, // أسبوعياً

  @HiveField(2)
  monthly, // شهرياً

  @HiveField(3)
  yearly, // سنوياً
}

/// سجل مؤرشف للمدفوعات
@HiveType(typeId: 29)
class ArchivedPaymentRecord extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String studentId;

  @HiveField(2)
  String studentName;

  @HiveField(3)
  String groupId;

  @HiveField(4)
  String groupName;

  @HiveField(5)
  double totalAmount;

  @HiveField(6)
  double paidAmount;

  @HiveField(7)
  DateTime periodStart; // بداية الفترة

  @HiveField(8)
  DateTime periodEnd; // نهاية الفترة

  @HiveField(9)
  DateTime archivedAt; // تاريخ الأرشفة

  @HiveField(10)
  List<PaymentTransaction> transactions;

  @HiveField(11)
  String? notes;

  @HiveField(12)
  PaymentStatus finalStatus; // الحالة النهائية عند الأرشفة

  ArchivedPaymentRecord({
    required this.id,
    required this.studentId,
    required this.studentName,
    required this.groupId,
    required this.groupName,
    required this.totalAmount,
    required this.paidAmount,
    required this.periodStart,
    required this.periodEnd,
    required this.archivedAt,
    required this.transactions,
    this.notes,
    required this.finalStatus,
  });

  /// المبلغ المتبقي
  double get remainingAmount => totalAmount - paidAmount;

  /// هل تم السداد بالكامل
  bool get isFullyPaid => remainingAmount <= 0;

  /// نسبة السداد
  double get paymentProgress => totalAmount > 0 ? paidAmount / totalAmount : 0;

  /// وصف الفترة
  String get periodDescription {
    if (periodStart.year == periodEnd.year &&
        periodStart.month == periodEnd.month &&
        periodStart.day == periodEnd.day) {
      return '${periodStart.day}/${periodStart.month}/${periodStart.year}';
    } else if (periodStart.year == periodEnd.year &&
        periodStart.month == periodEnd.month) {
      return '${periodStart.month}/${periodStart.year}';
    } else if (periodStart.year == periodEnd.year) {
      return 'سنة ${periodStart.year}';
    } else {
      return '${periodStart.day}/${periodStart.month}/${periodStart.year} - ${periodEnd.day}/${periodEnd.month}/${periodEnd.year}';
    }
  }
}
