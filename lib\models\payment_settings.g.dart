// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_settings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PaymentSettingsAdapter extends TypeAdapter<PaymentSettings> {
  @override
  final int typeId = 26;

  @override
  PaymentSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentSettings(
      calculationMethod: fields[0] as PaymentCalculationMethod,
      sessionsCount: fields[1] as int,
      defaultAmount: fields[2] as double,
      archiveFrequency: fields[3] as ArchiveFrequency,
      autoArchiveEnabled: fields[4] as bool,
      lastArchiveDate: fields[5] as DateTime?,
      integrateWithLessons: fields[6] as bool,
      overdueGraceDays: fields[7] as int,
      overdueCalculationMethod: fields[9] as OverdueCalculationMethod,
      createdAt: fields[8] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentSettings obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.calculationMethod)
      ..writeByte(1)
      ..write(obj.sessionsCount)
      ..writeByte(2)
      ..write(obj.defaultAmount)
      ..writeByte(3)
      ..write(obj.archiveFrequency)
      ..writeByte(4)
      ..write(obj.autoArchiveEnabled)
      ..writeByte(5)
      ..write(obj.lastArchiveDate)
      ..writeByte(6)
      ..write(obj.integrateWithLessons)
      ..writeByte(7)
      ..write(obj.overdueGraceDays)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.overdueCalculationMethod);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ArchivedPaymentRecordAdapter extends TypeAdapter<ArchivedPaymentRecord> {
  @override
  final int typeId = 29;

  @override
  ArchivedPaymentRecord read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ArchivedPaymentRecord(
      id: fields[0] as String,
      studentId: fields[1] as String,
      studentName: fields[2] as String,
      groupId: fields[3] as String,
      groupName: fields[4] as String,
      totalAmount: fields[5] as double,
      paidAmount: fields[6] as double,
      periodStart: fields[7] as DateTime,
      periodEnd: fields[8] as DateTime,
      archivedAt: fields[9] as DateTime,
      transactions: (fields[10] as List).cast<PaymentTransaction>(),
      notes: fields[11] as String?,
      finalStatus: fields[12] as PaymentStatus,
    );
  }

  @override
  void write(BinaryWriter writer, ArchivedPaymentRecord obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.studentId)
      ..writeByte(2)
      ..write(obj.studentName)
      ..writeByte(3)
      ..write(obj.groupId)
      ..writeByte(4)
      ..write(obj.groupName)
      ..writeByte(5)
      ..write(obj.totalAmount)
      ..writeByte(6)
      ..write(obj.paidAmount)
      ..writeByte(7)
      ..write(obj.periodStart)
      ..writeByte(8)
      ..write(obj.periodEnd)
      ..writeByte(9)
      ..write(obj.archivedAt)
      ..writeByte(10)
      ..write(obj.transactions)
      ..writeByte(11)
      ..write(obj.notes)
      ..writeByte(12)
      ..write(obj.finalStatus);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ArchivedPaymentRecordAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentCalculationMethodAdapter
    extends TypeAdapter<PaymentCalculationMethod> {
  @override
  final int typeId = 27;

  @override
  PaymentCalculationMethod read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 1:
        return PaymentCalculationMethod.monthly;
      default:
        return PaymentCalculationMethod.monthly;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentCalculationMethod obj) {
    switch (obj) {
      case PaymentCalculationMethod.monthly:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentCalculationMethodAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OverdueCalculationMethodAdapter
    extends TypeAdapter<OverdueCalculationMethod> {
  @override
  final int typeId = 29;

  @override
  OverdueCalculationMethod read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return OverdueCalculationMethod.monthStart;
      case 1:
        return OverdueCalculationMethod.monthEnd;
      case 2:
        return OverdueCalculationMethod.sessionsCount;
      default:
        return OverdueCalculationMethod.monthStart;
    }
  }

  @override
  void write(BinaryWriter writer, OverdueCalculationMethod obj) {
    switch (obj) {
      case OverdueCalculationMethod.monthStart:
        writer.writeByte(0);
        break;
      case OverdueCalculationMethod.monthEnd:
        writer.writeByte(1);
        break;
      case OverdueCalculationMethod.sessionsCount:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OverdueCalculationMethodAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ArchiveFrequencyAdapter extends TypeAdapter<ArchiveFrequency> {
  @override
  final int typeId = 28;

  @override
  ArchiveFrequency read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ArchiveFrequency.daily;
      case 1:
        return ArchiveFrequency.weekly;
      case 2:
        return ArchiveFrequency.monthly;
      case 3:
        return ArchiveFrequency.yearly;
      default:
        return ArchiveFrequency.daily;
    }
  }

  @override
  void write(BinaryWriter writer, ArchiveFrequency obj) {
    switch (obj) {
      case ArchiveFrequency.daily:
        writer.writeByte(0);
        break;
      case ArchiveFrequency.weekly:
        writer.writeByte(1);
        break;
      case ArchiveFrequency.monthly:
        writer.writeByte(2);
        break;
      case ArchiveFrequency.yearly:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ArchiveFrequencyAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
