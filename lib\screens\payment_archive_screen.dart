import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../theme/simple_theme.dart';
import '../providers/app_provider.dart';
import '../providers/student_payment_provider.dart';
import '../models/group.dart';
import '../models/student_payment.dart';

class PaymentReportsScreen extends StatefulWidget {
  const PaymentReportsScreen({super.key});

  @override
  State<PaymentReportsScreen> createState() => _PaymentReportsScreenState();
}

class _PaymentReportsScreenState extends State<PaymentReportsScreen> {
  Group? _selectedGroup;

  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: SimpleTheme.getBackgroundColor(context),
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            _buildHeader(),
            _buildMonthNavigation(),
            _buildStatistics(),
            Expanded(child: _buildArchiveList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.purple.withValues(alpha: 0.1),
            Colors.purple.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(
              Icons.arrow_back,
              color: SimpleTheme.getIconColor(context),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.analytics, color: Colors.blue, size: 28),
                    const SizedBox(width: 12),
                    Text(
                      'تقارير المدفوعات',
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'عرض تفاصيل المدفوعات لكل مجموعة وفترة',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: SimpleTheme.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ),
          ),
          // زر التصدير
          IconButton(
            onPressed: () => _exportReport(),
            icon: Icon(Icons.download, color: Colors.green),
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
    );
  }

  Widget _buildMonthNavigation() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          _buildGroupSelector(),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: SimpleTheme.getCardColor(context),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: SimpleTheme.getBorderColor(context)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  onPressed: () {
                    setState(() {
                      _selectedDate = DateTime(_selectedDate.year, _selectedDate.month - 1);
                    });
                  },
                  icon: Icon(Icons.arrow_back_ios, color: SimpleTheme.getIconColor(context)),
                ),
                Text(
                  _getMonthYearText(_selectedDate),
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: SimpleTheme.getTextColor(context),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _selectedDate = DateTime(_selectedDate.year, _selectedDate.month + 1);
                    });
                  },
                  icon: Icon(Icons.arrow_forward_ios, color: SimpleTheme.getIconColor(context)),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getMonthYearText(DateTime date) {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  Widget _buildGroupSelector() {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: SimpleTheme.getBorderColor(context)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<Group>(
              value: _selectedGroup,
              hint: Text(
                'جميع المجموعات',
                style: GoogleFonts.cairo(
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              ),
              isExpanded: true,
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: SimpleTheme.getIconColor(context),
              ),
              items: [
                DropdownMenuItem<Group>(
                  value: null,
                  child: Text(
                    'جميع المجموعات',
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                    ),
                  ),
                ),
                ...provider.groups.map((group) {
                  return DropdownMenuItem<Group>(
                    value: group,
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: Colors.purple,
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            group.name,
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.w600,
                              color: SimpleTheme.getTextColor(context),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ],
              onChanged: (Group? group) {
                setState(() {
                  _selectedGroup = group;
                });
              },
            ),
          ),
        );
      },
    );
  }



  Widget _buildStatistics() {
    return Consumer2<StudentPaymentProvider, AppProvider>(
      builder: (context, paymentProvider, appProvider, child) {
        final currentPayments = paymentProvider.payments;

        // فلترة حسب المجموعة المختارة
        final filteredPayments = _selectedGroup != null
            ? currentPayments
                  .where((p) => p.groupId == _selectedGroup!.id)
                  .toList()
            : currentPayments;

        // فلترة حسب الشهر المختار
        final filteredByPeriod = _filterPaymentsByMonth(filteredPayments);

        final totalRecords = filteredByPeriod.length;
        final totalPaid = filteredByPeriod.fold<double>(
          0,
          (sum, p) => sum + p.paidAmount,
        );
        final totalRemaining = filteredByPeriod.fold<double>(
          0,
          (sum, p) => sum + p.remainingAmount,
        );
        final settings = paymentProvider.settings;
        final appProvider = context.read<AppProvider>();

        final overdueCount = filteredByPeriod.where((p) {
          // حساب عدد الحصص التي حضرها الطالب
          final studentAttendedSessions = appProvider.lessons
              .where(
                (lesson) =>
                    lesson.groupId == p.groupId &&
                    lesson.attendedStudentIds.contains(p.studentId) &&
                    lesson.isCompleted,
              )
              .length;

          return p.isOverdueWithNewSettings(
            overdueMethod: settings.overdueCalculationMethod,
            sessionsCount: settings.sessionsCount,
            graceDays: settings.overdueGraceDays,
            studentAttendedSessions: studentAttendedSessions,
          );
        }).length;

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: SimpleTheme.getBorderColor(context)),
          ),
          child: Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'إجمالي الطلاب',
                  '$totalRecords طالب',
                  Icons.people,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatItem(
                  'مدفوع',
                  '${totalPaid.toStringAsFixed(0)} ج.م',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatItem(
                  'متبقي',
                  '${totalRemaining.toStringAsFixed(0)} ج.م',
                  Icons.pending,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatItem(
                  'متأخر',
                  '$overdueCount طالب',
                  Icons.warning,
                  Colors.red,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 10,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildArchiveList() {
    return Consumer2<StudentPaymentProvider, AppProvider>(
      builder: (context, paymentProvider, appProvider, child) {
        final currentPayments = paymentProvider.payments;

        // فلترة حسب المجموعة المختارة
        final filteredPayments = _selectedGroup != null
            ? currentPayments
                  .where((p) => p.groupId == _selectedGroup!.id)
                  .toList()
            : currentPayments;

        // فلترة حسب الشهر المختار
        final filteredByPeriod = _filterPaymentsByMonth(filteredPayments);

        if (filteredByPeriod.isEmpty) {
          return Container(
            margin: const EdgeInsets.all(20),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    size: 80,
                    color: SimpleTheme.getSecondaryTextColor(context),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'لا توجد مدفوعات في هذه الفترة',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: SimpleTheme.getSecondaryTextColor(context),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'اختر مجموعة أو فترة أخرى لعرض التقارير',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: SimpleTheme.getSecondaryTextColor(context),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: filteredByPeriod.length,
          itemBuilder: (context, index) {
            final payment = filteredByPeriod[index];
            return _buildPaymentCard(payment, appProvider);
          },
        );
      },
    );
  }

  List<StudentPayment> _filterPaymentsByMonth(List<StudentPayment> payments) {
    return payments.where((p) {
      return p.createdAt.year == _selectedDate.year && 
             p.createdAt.month == _selectedDate.month;
    }).toList();
  }

  Widget _buildPaymentCard(StudentPayment payment, AppProvider appProvider) {
    // حساب التأخير باستخدام النظام الجديد
    final paymentProvider = context.read<StudentPaymentProvider>();
    final settings = paymentProvider.settings;

    // حساب عدد الحصص التي حضرها الطالب
    final studentAttendedSessions = appProvider.lessons
        .where(
          (lesson) =>
              lesson.groupId == payment.groupId &&
              lesson.attendedStudentIds.contains(payment.studentId) &&
              lesson.isCompleted,
        )
        .length;

    final isOverdue = payment.isOverdueWithNewSettings(
      overdueMethod: settings.overdueCalculationMethod,
      sessionsCount: settings.sessionsCount,
      graceDays: settings.overdueGraceDays,
      studentAttendedSessions: studentAttendedSessions,
    );

    final statusColor = payment.isFullyPaid
        ? Colors.green
        : isOverdue
        ? Colors.red
        : Colors.orange;

    final statusText = payment.isFullyPaid
        ? 'مدفوع بالكامل'
        : isOverdue
        ? 'متأخر'
        : 'مدفوع جزئياً';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: statusColor.withValues(alpha: 0.3), width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Icon(Icons.person, color: statusColor, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      payment.studentName,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      payment.groupName,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  statusText,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: statusColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'المبلغ الإجمالي',
                  '${payment.totalAmount.toStringAsFixed(0)} ج.م',
                  Icons.account_balance_wallet,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'المدفوع',
                  '${payment.paidAmount.toStringAsFixed(0)} ج.م',
                  Icons.check_circle,
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  'المتبقي',
                  '${payment.remainingAmount.toStringAsFixed(0)} ج.م',
                  Icons.pending,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.date_range,
                size: 16,
                color: SimpleTheme.getSecondaryTextColor(context),
              ),
              const SizedBox(width: 8),
              Text(
                'تاريخ الإنشاء: ${payment.createdAt.day}/${payment.createdAt.month}/${payment.createdAt.year}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              ),
              const Spacer(),
              if (payment.transactions.isNotEmpty) ...[
                Icon(
                  Icons.receipt,
                  size: 16,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
                const SizedBox(width: 4),
                Text(
                  '${payment.transactions.length} معاملة',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: SimpleTheme.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String title, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 20, color: SimpleTheme.getSecondaryTextColor(context)),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: SimpleTheme.getTextColor(context),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 2),
        Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 10,
            color: SimpleTheme.getSecondaryTextColor(context),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  void _exportReport() {
    // عرض رسالة للمستخدم
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'ميزة التصدير ستكون متاحة قريباً',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.blue,
        action: SnackBarAction(
          label: 'حسناً',
          textColor: Colors.white,
          onPressed: () {},
        ),
      ),
    );
  }
}
