import 'package:hive_flutter/hive_flutter.dart';
import '../models/student.dart';
import '../models/group.dart';
import '../models/lesson.dart';
import '../models/student_payment.dart';

class DataService {
  static const String studentsBox = 'students';
  static const String groupsBox = 'groups';
  static const String lessonsBox = 'lessons';
  static const String studentPaymentsBox = 'student_payments';

  static Future<void> init() async {
    await Hive.initFlutter();

    Hive.registerAdapter(StudentAdapter());
    Hive.registerAdapter(GroupAdapter());
    Hive.registerAdapter(LessonAdapter());

    // تسجيل adapters نظام الدفع الجديد
    Hive.registerAdapter(StudentPaymentAdapter());
    Hive.registerAdapter(PaymentTransactionAdapter());
    Hive.registerAdapter(PaymentStatusAdapter());

    await Hive.openBox<Student>(studentsBox);
    await Hive.openBox<Group>(groupsBox);
    await Hive.openBox<Lesson>(lessonsBox);
    await Hive.openBox<StudentPayment>(studentPaymentsBox);
  }

  static Box<Student> get students => Hive.box<Student>(studentsBox);
  static Box<Group> get groups => Hive.box<Group>(groupsBox);
  static Box<Lesson> get lessons => Hive.box<Lesson>(lessonsBox);

  static Future<void> addStudent(Student student) async {
    await students.put(student.id, student);
  }

  static Future<void> addGroup(Group group) async {
    await groups.put(group.id, group);
  }

  static Future<void> addLesson(Lesson lesson) async {
    await lessons.put(lesson.id, lesson);
  }

  static Future<void> updateStudent(Student student) async {
    await students.put(student.id, student);
  }

  static Future<void> updateGroup(Group group) async {
    await groups.put(group.id, group);
  }

  static Future<void> updateLesson(Lesson lesson) async {
    await lessons.put(lesson.id, lesson);
  }

  static Future<void> deleteStudent(String id) async {
    await students.delete(id);
  }

  static Future<void> deleteGroup(String id) async {
    await groups.delete(id);
  }

  static Future<void> deleteLesson(String id) async {
    await lessons.delete(id);
  }

  static List<Student> getStudentsByGroup(String groupId) {
    return students.values.where((s) => s.groupId == groupId).toList();
  }

  static List<Lesson> getLessonsByDate(DateTime date) {
    return lessons.values
        .where(
          (l) =>
              l.dateTime.year == date.year &&
              l.dateTime.month == date.month &&
              l.dateTime.day == date.day,
        )
        .toList();
  }
}
