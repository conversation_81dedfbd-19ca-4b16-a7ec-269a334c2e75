import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/student_payment.dart';
import '../models/payment_settings.dart';
import '../models/student.dart';
import '../models/group.dart';
import '../models/lesson.dart';

class StudentPaymentProvider extends ChangeNotifier {
  Box<StudentPayment>? _paymentsBox;
  List<StudentPayment> _payments = [];
  PaymentSettings? _settings;
  final List<ArchivedPaymentRecord> _archivedRecords = [];

  List<StudentPayment> get payments => _payments;
  List<ArchivedPaymentRecord> get archivedRecords => _archivedRecords;
  PaymentSettings get settings =>
      _settings ?? PaymentSettings.defaultSettings();

  /// تهيئة المزود
  Future<void> initialize() async {
    try {
      // فتح صندوق المدفوعات
      if (Hive.isBoxOpen('student_payments')) {
        _paymentsBox = Hive.box<StudentPayment>('student_payments');
      } else {
        _paymentsBox = await Hive.openBox<StudentPayment>('student_payments');
      }

      // تحميل الإعدادات
      await _loadSettings();

      _loadPayments();
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing StudentPaymentProvider: $e');
      _payments = [];
      _settings = PaymentSettings.defaultSettings();
    }
  }

  /// تحميل الإعدادات من التخزين المحلي
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحميل الإعدادات من SharedPreferences
      final calculationMethodIndex =
          prefs.getInt('payment_calculation_method') ?? 0;
      final sessionsCount = prefs.getInt('payment_sessions_count') ?? 8;
      final defaultAmount = prefs.getDouble('payment_default_amount') ?? 100.0;
      final archiveFrequencyIndex =
          prefs.getInt('payment_archive_frequency') ?? 2;
      final autoArchiveEnabled = prefs.getBool('payment_auto_archive') ?? true;
      final integrateWithLessons =
          prefs.getBool('payment_integrate_lessons') ?? true;
      final overdueGraceDays = prefs.getInt('payment_overdue_grace_days') ?? 3;
      final lastArchiveDateMs = prefs.getInt('payment_last_archive_date');

      _settings = PaymentSettings(
        calculationMethod:
            PaymentCalculationMethod.values[calculationMethodIndex],
        sessionsCount: sessionsCount,
        defaultAmount: defaultAmount,
        archiveFrequency: ArchiveFrequency.values[archiveFrequencyIndex],
        autoArchiveEnabled: autoArchiveEnabled,
        integrateWithLessons: integrateWithLessons,
        overdueGraceDays: overdueGraceDays,
        lastArchiveDate: lastArchiveDateMs != null
            ? DateTime.fromMillisecondsSinceEpoch(lastArchiveDateMs)
            : DateTime.now(),
        createdAt: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error loading settings: $e');
      _settings = PaymentSettings.defaultSettings();
    }
  }

  /// تحميل المدفوعات
  void _loadPayments() {
    if (_paymentsBox != null) {
      _payments = _paymentsBox!.values.toList();
    }
  }

  /// الحصول على سجل دفعات طالب معين
  StudentPayment? getStudentPayment(String studentId) {
    try {
      return _payments.firstWhere((p) => p.studentId == studentId);
    } catch (e) {
      return null;
    }
  }

  /// إنشاء سجل دفعات جديد للطالب
  Future<void> createStudentPayment({
    required String studentId,
    required String studentName,
    required String groupId,
    required String groupName,
    required double totalAmount,
    String? notes,
  }) async {
    if (_paymentsBox == null) return;

    // التحقق من عدم وجود سجل مسبق
    final existingPayment = getStudentPayment(studentId);
    if (existingPayment != null) {
      throw Exception('يوجد سجل دفعات مسبق لهذا الطالب');
    }

    final payment = StudentPayment(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      studentId: studentId,
      studentName: studentName,
      groupId: groupId,
      groupName: groupName,
      totalAmount: totalAmount,
      createdAt: DateTime.now(),
      notes: notes,
    );

    await _paymentsBox!.add(payment);
    _loadPayments();
    notifyListeners();
  }

  /// إضافة دفعة للطالب
  Future<void> addPaymentToStudent({
    required String studentId,
    required double amount,
    String? notes,
  }) async {
    final payment = getStudentPayment(studentId);
    if (payment == null) {
      throw Exception('لا يوجد سجل دفعات لهذا الطالب');
    }

    payment.addPayment(amount, notes: notes);
    await payment.save();
    _loadPayments();
    notifyListeners();
  }

  /// حذف معاملة دفع
  Future<void> removeTransaction({
    required String studentId,
    required String transactionId,
  }) async {
    final payment = getStudentPayment(studentId);
    if (payment == null) return;

    payment.removeTransaction(transactionId);
    await payment.save();
    _loadPayments();
    notifyListeners();
  }

  /// تحديث المبلغ الإجمالي للطالب
  Future<void> updateTotalAmount({
    required String studentId,
    required double newTotalAmount,
  }) async {
    final payment = getStudentPayment(studentId);
    if (payment == null) return;

    payment.totalAmount = newTotalAmount;
    await payment.save();
    _loadPayments();
    notifyListeners();
  }

  /// حذف سجل دفعات طالب
  Future<void> deleteStudentPayment(String studentId) async {
    final payment = getStudentPayment(studentId);
    if (payment == null) return;

    await payment.delete();
    _loadPayments();
    notifyListeners();
  }

  /// الحصول على معلومات دفعات طلاب مجموعة معينة
  List<StudentPaymentInfo> getGroupPaymentInfo(
    String groupId,
    List<Student> students,
    List<Lesson> lessons,
  ) {
    return students.map((student) {
      final payment = getStudentPayment(student.id);

      // حساب عدد الحصص التي حضرها الطالب (باستثناء الملغية)
      final studentAttendedSessions = lessons
          .where(
            (lesson) =>
                lesson.groupId == groupId &&
                lesson.attendedStudentIds.contains(student.id) &&
                lesson.isCompleted &&
                !lesson.isCancelled,
          )
          .length;

      // حساب تواريخ الحصص
      final studentLessons = lessons.where(
        (l) => l.groupId == groupId && 
               l.attendedStudentIds.contains(student.id) && 
               l.isCompleted && 
               !l.isCancelled
      ).toList();
      
      DateTime? firstLessonDate;
      DateTime? lastLessonDate;
      
      if (studentLessons.isNotEmpty) {
        studentLessons.sort((a, b) => a.dateTime.compareTo(b.dateTime));
        firstLessonDate = studentLessons.first.dateTime;
        lastLessonDate = studentLessons.last.dateTime;
      }

      // حساب التأخير باستخدام النظام الجديد
      bool isOverdue = false;
      if (payment != null && !payment.isFullyPaid) {
        isOverdue = payment.isOverdueWithNewSettings(
          overdueMethod: settings.overdueCalculationMethod,
          sessionsCount: settings.sessionsCount,
          graceDays: settings.overdueGraceDays,
          studentAttendedSessions: studentAttendedSessions,
          firstLessonDate: firstLessonDate,
          lastLessonDate: lastLessonDate,
        );
      }

      // إذا لم يوجد سجل دفعات ولديه حصص غير ملغية، فهو متأخر
      if (payment == null && studentAttendedSessions > 0) {
        isOverdue = true;
      }

      return StudentPaymentInfo(
        studentId: student.id,
        studentName: student.name,
        groupName: '', // سيتم تعبئته من المجموعة
        payment: payment,
        totalPaid: payment?.paidAmount ?? 0,
        totalRemaining: payment?.remainingAmount ?? (studentAttendedSessions > 0 ? settings.defaultAmount : 0.0),
        isOverdue: isOverdue,
      );
    }).toList();
  }

  /// حساب الإحصائيات
  PaymentStatistics calculateStatistics(List<Lesson> lessons) {
    double totalPaid = 0;
    double totalOverdue = 0;
    int paidStudents = 0;
    int overdueStudents = 0;
    Map<String, double> revenueByGroup = {};

    for (final payment in _payments) {
      totalPaid += payment.paidAmount;

      if (!payment.isFullyPaid) {
        totalOverdue += payment.remainingAmount;
      }

      if (payment.isFullyPaid) {
        paidStudents++;
      } else {
        // حساب عدد الحصص التي حضرها الطالب
        final studentAttendedSessions = lessons
            .where(
              (lesson) =>
                  lesson.groupId == payment.groupId &&
                  lesson.attendedStudentIds.contains(payment.studentId) &&
                  lesson.isCompleted,
            )
            .length;

        // حساب تواريخ الحصص
        final studentLessons = lessons.where(
          (l) => l.groupId == payment.groupId && 
                 l.attendedStudentIds.contains(payment.studentId) && 
                 l.isCompleted && 
                 !l.isCancelled
        ).toList();
        
        DateTime? firstLessonDate;
        DateTime? lastLessonDate;
        
        if (studentLessons.isNotEmpty) {
          studentLessons.sort((a, b) => a.dateTime.compareTo(b.dateTime));
          firstLessonDate = studentLessons.first.dateTime;
          lastLessonDate = studentLessons.last.dateTime;
        }

        // حساب التأخير باستخدام النظام الجديد
        final isOverdue = payment.isOverdueWithNewSettings(
          overdueMethod: settings.overdueCalculationMethod,
          sessionsCount: settings.sessionsCount,
          graceDays: settings.overdueGraceDays,
          studentAttendedSessions: studentAttendedSessions,
          firstLessonDate: firstLessonDate,
          lastLessonDate: lastLessonDate,
        );
        if (isOverdue) {
          overdueStudents++;
        }
      }

      // إحصائيات المجموعة
      final groupRevenue = revenueByGroup[payment.groupName] ?? 0;
      revenueByGroup[payment.groupName] = groupRevenue + payment.paidAmount;
    }

    return PaymentStatistics(
      totalPaid: totalPaid,
      totalOverdue: totalOverdue,
      totalStudents: _payments.length,
      paidStudents: paidStudents,
      overdueStudents: overdueStudents,
      revenueByGroup: revenueByGroup,
    );
  }

  /// إنشاء سجلات دفعات تلقائية للطلاب الجدد
  Future<void> createPaymentsForNewStudents(
    List<Student> students,
    List<Group> groups,
  ) async {
    for (final student in students) {
      final existingPayment = getStudentPayment(student.id);
      if (existingPayment != null) continue;

      final group = groups.where((g) => g.id == student.groupId).firstOrNull;
      if (group == null) continue;

      final totalAmount = group.monthlyFee > 0 ? group.monthlyFee : settings.defaultAmount;

      await createStudentPayment(
        studentId: student.id,
        studentName: student.name,
        groupId: group.id,
        groupName: group.name,
        totalAmount: totalAmount,
        notes: 'تم إنشاؤه تلقائياً',
      );
    }
  }

  /// البحث في المدفوعات
  List<StudentPayment> searchPayments(String query) {
    if (query.isEmpty) return _payments;

    return _payments.where((payment) {
      return payment.studentName.toLowerCase().contains(query.toLowerCase()) ||
          payment.groupName.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// فلترة المدفوعات حسب الحالة
  List<StudentPayment> filterPaymentsByStatus(PaymentStatus? status) {
    if (status == null) return _payments;

    return _payments.where((payment) {
      // تحديث حالة الدفع بناءً على الوقت الحالي
      if (payment.isFullyPaid) {
        return status == PaymentStatus.paid;
      } else if (payment.isOverdue) {
        return status == PaymentStatus.overdue;
      } else {
        return status == PaymentStatus.pending;
      }
    }).toList();
  }

  /// فلترة المدفوعات حسب المجموعة
  List<StudentPayment> filterPaymentsByGroup(String groupId) {
    return _payments.where((payment) => payment.groupId == groupId).toList();
  }

  /// تحديث الإعدادات
  Future<void> updateSettings(PaymentSettings newSettings) async {
    try {
      _settings = newSettings;

      // حفظ الإعدادات في SharedPreferences
      final prefs = await SharedPreferences.getInstance();

      await prefs.setInt(
        'payment_calculation_method',
        newSettings.calculationMethod.index,
      );
      await prefs.setInt('payment_sessions_count', newSettings.sessionsCount);
      await prefs.setDouble(
        'payment_default_amount',
        newSettings.defaultAmount,
      );
      await prefs.setInt(
        'payment_archive_frequency',
        newSettings.archiveFrequency.index,
      );
      await prefs.setBool(
        'payment_auto_archive',
        newSettings.autoArchiveEnabled,
      );
      await prefs.setBool(
        'payment_integrate_lessons',
        newSettings.integrateWithLessons,
      );
      await prefs.setInt(
        'payment_overdue_grace_days',
        newSettings.overdueGraceDays,
      );
      await prefs.setInt(
        'payment_last_archive_date',
        newSettings.lastArchiveDate.millisecondsSinceEpoch,
      );

      notifyListeners();
    } catch (e) {
      debugPrint('Error saving settings: $e');
      throw Exception('فشل في حفظ الإعدادات');
    }
  }

  /// التحقق من الطلاب المتأخرين بناءً على الدروس
  Future<void> checkOverdueStudentsWithLessons(
    List<Lesson> lessons,
    List<Student> students,
    List<Group> groups,
  ) async {
    if (!settings.integrateWithLessons) return;

    for (final student in students) {
      final group = groups.where((g) => g.id == student.groupId).firstOrNull;
      if (group == null) continue;

      final payment = getStudentPayment(student.id);
      if (payment != null && payment.isFullyPaid) continue;

      final studentLessons = lessons
          .where(
            (l) =>
                l.groupId == group.id &&
                l.attendedStudentIds.contains(student.id) &&
                l.isCompleted,
          )
          .toList();

      bool shouldCreatePayment = false;

      // إنشاء سجل دفعات إذا حضر الطالب حصصاً ولم يوجد له سجل
      if (studentLessons.isNotEmpty && payment == null) {
        shouldCreatePayment = true;
      }

      if (shouldCreatePayment) {
        await createStudentPayment(
          studentId: student.id,
          studentName: student.name,
          groupId: group.id,
          groupName: group.name,
          totalAmount: group.monthlyFee > 0
              ? group.monthlyFee
              : settings.defaultAmount,
          notes: 'تم إنشاؤه تلقائياً بناءً على الحضور',
        );
      }
    }
  }

  /// أرشفة المدفوعات القديمة
  Future<void> archiveOldPayments() async {
    if (!settings.shouldArchive) return;

    try {
      final now = DateTime.now();
      final paymentsToArchive = <StudentPayment>[];

      for (final payment in _payments) {
        bool shouldArchive = false;

        switch (settings.archiveFrequency) {
          case ArchiveFrequency.daily:
            shouldArchive = now.difference(payment.createdAt).inDays >= 1;
            break;
          case ArchiveFrequency.weekly:
            shouldArchive = now.difference(payment.createdAt).inDays >= 7;
            break;
          case ArchiveFrequency.monthly:
            shouldArchive = now.difference(payment.createdAt).inDays >= 30;
            break;
          case ArchiveFrequency.yearly:
            shouldArchive = now.difference(payment.createdAt).inDays >= 365;
            break;
        }

        if (shouldArchive) {
          paymentsToArchive.add(payment);
        }
      }

      // أرشفة المدفوعات
      final prefs = await SharedPreferences.getInstance();
      final archivedData = prefs.getStringList('archived_payments') ?? [];

      for (final payment in paymentsToArchive) {
        final archivedRecord = ArchivedPaymentRecord(
          id: payment.id,
          studentId: payment.studentId,
          studentName: payment.studentName,
          groupId: payment.groupId,
          groupName: payment.groupName,
          totalAmount: payment.totalAmount,
          paidAmount: payment.paidAmount,
          periodStart: payment.createdAt,
          periodEnd: payment.lastPaymentDate,
          archivedAt: now,
          transactions: payment.transactions,
          notes: payment.notes,
          finalStatus: payment.status,
        );

        // حفظ في الأرشيف المحلي
        _archivedRecords.add(archivedRecord);

        // حفظ في SharedPreferences كـ JSON
        final recordJson = _archivedRecordToJson(archivedRecord);
        archivedData.add(recordJson);

        // حذف من المدفوعات الحالية
        await payment.delete();
      }

      // حفظ الأرشيف المحدث
      await prefs.setStringList('archived_payments', archivedData);

      _loadPayments();

      // تحديث تاريخ آخر أرشفة
      final updatedSettings = PaymentSettings(
        calculationMethod: settings.calculationMethod,
        sessionsCount: settings.sessionsCount,
        defaultAmount: settings.defaultAmount,
        archiveFrequency: settings.archiveFrequency,
        autoArchiveEnabled: settings.autoArchiveEnabled,
        lastArchiveDate: now,
        integrateWithLessons: settings.integrateWithLessons,
        overdueGraceDays: settings.overdueGraceDays,
        createdAt: settings.createdAt,
      );

      await updateSettings(updatedSettings);

      notifyListeners();
    } catch (e) {
      debugPrint('Error archiving payments: $e');
    }
  }

  /// تحويل سجل الأرشيف إلى JSON
  String _archivedRecordToJson(ArchivedPaymentRecord record) {
    final Map<String, dynamic> json = {
      'id': record.id,
      'studentId': record.studentId,
      'studentName': record.studentName,
      'groupId': record.groupId,
      'groupName': record.groupName,
      'totalAmount': record.totalAmount,
      'paidAmount': record.paidAmount,
      'periodStart': record.periodStart.millisecondsSinceEpoch,
      'periodEnd': record.periodEnd.millisecondsSinceEpoch,
      'archivedAt': record.archivedAt.millisecondsSinceEpoch,
      'transactions': record.transactions
          .map(
            (t) => {
              'id': t.id,
              'amount': t.amount,
              'date': t.date.millisecondsSinceEpoch,
              'notes': t.notes,
            },
          )
          .toList(),
      'notes': record.notes,
      'finalStatus': record.finalStatus.index,
    };

    return json.toString();
  }

  /// تحميل الأرشيف من التخزين المحلي
  Future<void> loadArchivedRecords() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final archivedData = prefs.getStringList('archived_payments') ?? [];

      _archivedRecords.clear();

      for (final jsonString in archivedData) {
        try {
          final record = _archivedRecordFromJson(jsonString);
          _archivedRecords.add(record);
        } catch (e) {
          debugPrint('Error parsing archived record: $e');
        }
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading archived records: $e');
    }
  }

  /// تحويل JSON إلى سجل أرشيف
  ArchivedPaymentRecord _archivedRecordFromJson(String jsonString) {
    // تحويل مبسط - في التطبيق الحقيقي يفضل استخدام مكتبة JSON
    final parts = jsonString
        .replaceAll('{', '')
        .replaceAll('}', '')
        .split(', ');
    final Map<String, String> data = {};

    for (final part in parts) {
      final keyValue = part.split(': ');
      if (keyValue.length == 2) {
        data[keyValue[0]] = keyValue[1];
      }
    }

    return ArchivedPaymentRecord(
      id: data['id'] ?? '',
      studentId: data['studentId'] ?? '',
      studentName: data['studentName'] ?? '',
      groupId: data['groupId'] ?? '',
      groupName: data['groupName'] ?? '',
      totalAmount: double.tryParse(data['totalAmount'] ?? '0') ?? 0,
      paidAmount: double.tryParse(data['paidAmount'] ?? '0') ?? 0,
      periodStart: DateTime.fromMillisecondsSinceEpoch(
        int.tryParse(data['periodStart'] ?? '0') ?? 0,
      ),
      periodEnd: DateTime.fromMillisecondsSinceEpoch(
        int.tryParse(data['periodEnd'] ?? '0') ?? 0,
      ),
      archivedAt: DateTime.fromMillisecondsSinceEpoch(
        int.tryParse(data['archivedAt'] ?? '0') ?? 0,
      ),
      transactions: [], // مبسط للآن
      notes: data['notes'],
      finalStatus:
          PaymentStatus.values[int.tryParse(data['finalStatus'] ?? '0') ?? 0],
    );
  }
}
