import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';
import '../theme/simple_theme.dart';

import '../services/data_service.dart';
import '../widgets/profile_avatar.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_button.dart';
import 'backup_manager_screen.dart';
import 'check_updates_screen.dart';
import 'privacy_policy_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          body: SafeArea(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                children: [
                  // Profile Header
                  _buildProfileHeader(provider),

                  // Settings Content
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: SimpleTheme.getBackgroundColor(context),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: SimpleTheme.getBorderColor(context),
                            blurRadius: 10,
                            offset: const Offset(0, -5),
                          ),
                        ],
                      ),
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        padding: const EdgeInsets.only(top: 30, bottom: 20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSettingsSection(context, provider),
                            const SizedBox(height: 20),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfileHeader(AppProvider provider) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 40),
      decoration: BoxDecoration(
        gradient: SimpleTheme.getBackgroundGradient(context),
      ),
      child: Row(
        children: [
          ProfileAvatar(
            size: 80,
            onTap: () {
              // تنفيذ تغيير الصورة الشخصية
            },
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  provider.teacherName,
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: SimpleTheme.getTextColor(context),
                  ),
                ),
                Text(
                  'معلم',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: SimpleTheme.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: SimpleTheme.getBorderColor(context),
              shape: BoxShape.circle,
            ),
            child: InkWell(
              onTap: () {
                // تنفيذ تعديل اسم المعلم
                _showEditNameDialog(context);
              },
              child: Icon(
                Icons.edit,
                color: SimpleTheme.getTextColor(context),
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(BuildContext context, AppProvider provider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // App Settings
          _buildSectionHeader('إعدادات التطبيق'),
          _buildSettingsTile(
            title: provider.isDarkMode ? 'المظهر الداكن' : 'المظهر الفاتح',
            icon: provider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
            iconColor: SimpleTheme.primary,
            trailing: Switch(
              value: provider.isDarkMode,
              onChanged: (value) async {
                await provider.toggleTheme();
              },
              activeColor: SimpleTheme.primary,
              inactiveTrackColor: Colors.grey.shade700,
            ),
          ),
          _buildSettingsTile(
            title: 'موقع شريط التنقل',
            icon: provider.isNavBarAtTop
                ? Icons.vertical_align_top
                : Icons.vertical_align_bottom,
            iconColor: SimpleTheme.primary,
            trailing: Switch(
              value: provider.isNavBarAtTop,
              onChanged: (value) async {
                await provider.toggleNavBarPosition();
              },
              activeColor: SimpleTheme.primary,
              inactiveTrackColor: Colors.grey.shade700,
            ),
          ),

          // Data Management
          const SizedBox(height: 20),
          _buildSectionHeader('إدارة البيانات'),
          _buildSettingsTile(
            title: 'إحصائيات البيانات',
            icon: Icons.analytics_outlined,
            iconColor: SimpleTheme.primary,
            onTap: () {
              _showDataStatsDialog(context, provider);
            },
          ),
          _buildSettingsTile(
            title: 'نسخة احتياطية',
            icon: Icons.backup_outlined,
            iconColor: SimpleTheme.primary,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const BackupManagerScreen(),
                ),
              );
            },
          ),
          _buildSettingsTile(
            title: 'مسح جميع البيانات',
            icon: Icons.delete_outline,
            iconColor: Colors.red,
            onTap: () {
              _showClearDataDialog(context, provider);
            },
          ),

          // About
          const SizedBox(height: 20),
          _buildSectionHeader('حول التطبيق'),
          _buildSettingsTile(
            title: 'إصدار التطبيق',
            subtitle: '1.0.0',
            icon: Icons.app_registration,
            iconColor: SimpleTheme.primary,
          ),
          _buildSettingsTile(
            title: 'فحص التحديثات',
            icon: Icons.system_update_outlined,
            iconColor: SimpleTheme.primary,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CheckUpdatesScreen(),
                ),
              );
            },
          ),
          _buildSettingsTile(
            title: 'سياسة الخصوصية',
            icon: Icons.privacy_tip_outlined,
            iconColor: SimpleTheme.primary,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PrivacyPolicyScreen(),
                ),
              );
            },
          ),
          _buildSettingsTile(
            title: 'المطور',
            subtitle: 'Mohamed Youssef / EduTrack Team',
            icon: Icons.code,
            iconColor: SimpleTheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: SimpleTheme.getTextColor(context),
        ),
      ),
    );
  }

  Widget _buildSettingsTile({
    required String title,
    String? subtitle,
    required IconData icon,
    Widget? trailing,
    VoidCallback? onTap,
    Color? iconColor,
  }) {
    return ModernCard(
      margin: const EdgeInsets.only(bottom: 15),
      padding: EdgeInsets.zero,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: (iconColor ?? SimpleTheme.primary).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(icon, color: iconColor ?? SimpleTheme.primary, size: 22),
        ),
        title: Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        subtitle: subtitle != null
            ? Text(
                subtitle,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              )
            : null,
        trailing:
            trailing ??
            (onTap != null
                ? Icon(
                    Icons.arrow_forward_ios,
                    color: SimpleTheme.getSecondaryTextColor(context),
                    size: 16,
                  )
                : null),
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      ),
    );
  }

  // عرض حوار إحصائيات البيانات
  void _showDataStatsDialog(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                const Color(0xFF1e293b),
                const Color(0xFF1e293b).withValues(alpha: 0.9),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: SimpleTheme.getBorderColor(context),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'إحصائيات البيانات',
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: SimpleTheme.getTextColor(context),
                ),
              ),
              const SizedBox(height: 20),
              _buildStatRow('إجمالي الطلاب', provider.totalStudents.toString()),
              _buildStatRow(
                'إجمالي المجموعات',
                provider.groups.length.toString(),
              ),
              _buildStatRow('إجمالي الحصص', provider.lessons.length.toString()),
              _buildStatRow(
                'المواد المختلفة',
                provider.groups.map((g) => g.subject).toSet().length.toString(),
              ),
              const SizedBox(height: 20),
              ModernButton(
                text: 'إغلاق',
                onPressed: () => Navigator.pop(context),
                isFullWidth: true,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
          ),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF6366f1),
            ),
          ),
        ],
      ),
    );
  }

  // عرض حوار مسح البيانات
  void _showClearDataDialog(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.getCardColor(context),
        title: Text(
          'تأكيد مسح البيانات',
          style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
        ),
        content: Text(
          'هل أنت متأكد من رغبتك في مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.',
          style: GoogleFonts.cairo(
            color: SimpleTheme.getSecondaryTextColor(context),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context).withValues(alpha: 0.7),
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              // مسح جميع البيانات
              await DataService.students.clear();
              await DataService.groups.clear();
              await DataService.lessons.clear();
              provider.loadData();
              if (!context.mounted) return;
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'تم مسح جميع البيانات بنجاح',
                    style: GoogleFonts.cairo(),
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

// عرض حوار تعديل اسم المعلم
void _showEditNameDialog(BuildContext context) {
  final provider = Provider.of<AppProvider>(context, listen: false);
  final TextEditingController nameController = TextEditingController(
    text: provider.teacherName,
  );

  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      backgroundColor: SimpleTheme.getCardColor(context),
      title: Text(
        'تعديل الاسم',
        style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
      ),
      content: TextField(
        controller: nameController,
        style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
        decoration: InputDecoration(
          hintText: 'أدخل الاسم',
          hintStyle: GoogleFonts.cairo(
            color: SimpleTheme.getTextColor(context).withValues(alpha: 0.7),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: Color(0xFF6366f1)),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'إلغاء',
            style: GoogleFonts.cairo(
              color: SimpleTheme.getTextColor(context).withValues(alpha: 0.7),
            ),
          ),
        ),
        TextButton(
          onPressed: () async {
            // حفظ الاسم الجديد
            final newName = nameController.text.trim();
            if (newName.isNotEmpty) {
              // حفظ الاسم في مزود البيانات
              await provider.setTeacherName(newName);
              if (!context.mounted) return;
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'تم تغيير الاسم بنجاح',
                    style: GoogleFonts.cairo(),
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'الرجاء إدخال اسم صحيح',
                    style: GoogleFonts.cairo(),
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          child: Text(
            'حفظ',
            style: GoogleFonts.cairo(color: const Color(0xFF6366f1)),
          ),
        ),
      ],
    ),
  );
}
