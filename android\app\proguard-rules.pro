# تحسينات متقدمة جداً لتقليل الحجم
-optimizationpasses 7
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*,!code/allocation/variable

# Flutter wrapper - محسن
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.plugins.** { *; }
-keep class io.flutter.plugin.editing.** { *; }

# Firebase
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# Hive
-keep class com.example.edu_track.models.** { *; }
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.SerializationKt
-keepclassmembers class kotlinx.serialization.json.** { *; }

# Flutter TTS
-keep class com.tundralabs.fluttertts.** { *; }

# Speech to text
-keep class com.csdcorp.speech_to_text.** { *; }

# File picker
-keep class com.mr.flutter.plugin.filepicker.** { *; }

# Connectivity plus
-keep class dev.fluttercommunity.plus.connectivity.** { *; }

# Permission handler
-keep class com.baseflow.permissionhandler.** { *; }

# Flutter local notifications
-keep class com.dexterous.flutterlocalnotifications.** { *; }

# General Android
-keep class androidx.lifecycle.** { *; }
-keep class androidx.core.app.** { *; }

# تحسينات إضافية لتقليل الحجم
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
}

# إزالة الكود غير المستخدم
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn sun.misc.Unsafe

# تحسين الكلاسات
-allowaccessmodification
-mergeinterfacesaggressively
-overloadaggressively
-repackageclasses ''

# إزالة المعلومات غير الضرورية
-renamesourcefileattribute SourceFile

# تحسينات إضافية متقدمة
-assumenosideeffects class java.lang.System {
    public static long currentTimeMillis();
    static java.lang.Class getCallerClass();
    public static int identityHashCode(java.lang.Object);
    public static java.lang.SecurityManager getSecurityManager();
    public static java.util.Properties getProperties();
    public static java.lang.String getProperty(java.lang.String);
    public static java.lang.String getenv(java.lang.String);
}

# إزالة التحقق من الأخطاء
-assumenosideeffects class java.lang.Throwable {
    public void printStackTrace();
}

# تحسين الذاكرة
-assumenosideeffects class java.lang.Class {
    public java.lang.String toString();
    public java.lang.String getName();
    public java.lang.String getSimpleName();
}

# إزالة كود التطوير
-assumenosideeffects class kotlin.jvm.internal.Intrinsics {
    static void checkParameterIsNotNull(java.lang.Object, java.lang.String);
    static void checkNotNullParameter(java.lang.Object, java.lang.String);
    static void checkExpressionValueIsNotNull(java.lang.Object, java.lang.String);
    static void checkNotNullExpressionValue(java.lang.Object, java.lang.String);
    static void checkReturnedValueIsNotNull(java.lang.Object, java.lang.String);
    static void checkFieldIsNotNull(java.lang.Object, java.lang.String);
}

# تجاهل التحذيرات
-dontwarn **
-ignorewarnings