import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';
import '../providers/student_payment_provider.dart';
import '../theme/simple_theme.dart';

class StudentsScreen extends StatefulWidget {
  const StudentsScreen({super.key});

  @override
  State<StudentsScreen> createState() => _StudentsScreenState();
}

class _StudentsScreenState extends State<StudentsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          body: Container(
            decoration: BoxDecoration(
              gradient: SimpleTheme.getBackgroundGradient(context),
            ),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header Section
                    _buildHeader(context, provider),
                    const SizedBox(height: 24),

                    // Stats Cards
                    _buildStatsCards(provider),
                    const SizedBox(height: 24),

                    // Search Section
                    _buildSearch(provider),
                    const SizedBox(height: 20),

                    // Students List
                    _buildStudentsList(provider),

                    // إضافة مساحة إضافية في الأسفل
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // بناء الهيدر
  Widget _buildHeader(BuildContext context, AppProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // العنوان والوصف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الطلاب',
                  style: GoogleFonts.cairo(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: SimpleTheme.getTextColor(context),
                    shadows: [
                      Shadow(
                        color: SimpleTheme.getContainerColor(context),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'عرض وإدارة جميع الطلاب',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: SimpleTheme.getSecondaryTextColor(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقات الإحصائيات البسيطة
  Widget _buildStatsCards(AppProvider provider) {
    final allStudents = provider.getAllStudents();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: SimpleTheme.getBorderColor(context),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _buildSimpleStatCard(
        'عدد الطلاب',
        allStudents.length.toString(),
        Icons.people_rounded,
        const Color(0xFF6366f1),
      ),
    );
  }

  Widget _buildSimpleStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 8),
            Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: SimpleTheme.getTextColor(context),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: SimpleTheme.getSecondaryTextColor(context),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // بناء البحث
  Widget _buildSearch(AppProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: SimpleTheme.getBorderColor(context)),
      ),
      child: TextField(
        style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
        decoration: InputDecoration(
          hintText: 'البحث عن طالب...',
          hintStyle: GoogleFonts.cairo(
            color: SimpleTheme.getSubtitleColor(context),
          ),
          border: InputBorder.none,
          icon: Icon(
            Icons.search,
            color: SimpleTheme.getSecondaryTextColor(context),
          ),
        ),
        onChanged: (value) => setState(() => _searchQuery = value),
      ),
    );
  }

  // بناء قائمة الطلاب
  Widget _buildStudentsList(AppProvider provider) {
    final allStudents = provider.getAllStudents();
    var filteredStudents = allStudents.where((student) {
      final matchesSearch =
          _searchQuery.isEmpty ||
          student.name.toLowerCase().contains(_searchQuery.toLowerCase());
      return matchesSearch;
    }).toList();

    if (filteredStudents.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'قائمة الطلاب (${filteredStudents.length})',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: filteredStudents.length,
          itemBuilder: (context, index) {
            final student = filteredStudents[index];
            final group = provider.groups.isNotEmpty
                ? provider.groups.firstWhere(
                    (g) => g.id == student.groupId,
                    orElse: () => provider.groups.first,
                  )
                : null;
            return _buildStudentCard(student, group, provider);
          },
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: SimpleTheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.people_rounded,
              size: 64,
              color: SimpleTheme.primary,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد طلاب بعد',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: SimpleTheme.getTextColor(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على الزر أدناه لإضافة طالب جديد',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  void _showEditStudentDialog(
    BuildContext context,
    AppProvider provider,
    student,
  ) {
    final nameController = TextEditingController(text: student.name);
    final phoneController = TextEditingController(text: student.phoneNumber ?? '');
    String? selectedGroupId = student.groupId;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.getCardColor(context),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          'تعديل الطالب',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
              decoration: InputDecoration(
                labelText: 'اسم الطالب',
                labelStyle: GoogleFonts.cairo(color: SimpleTheme.getSecondaryTextColor(context)),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: phoneController,
              style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
              decoration: InputDecoration(
                labelText: 'رقم الهاتف',
                labelStyle: GoogleFonts.cairo(color: SimpleTheme.getSecondaryTextColor(context)),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: selectedGroupId,
              style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
              decoration: InputDecoration(
                labelText: 'المجموعة',
                labelStyle: GoogleFonts.cairo(color: SimpleTheme.getSecondaryTextColor(context)),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
              ),
              items: provider.groups.map((group) {
                return DropdownMenuItem(
                  value: group.id,
                  child: Text(group.name, style: GoogleFonts.cairo()),
                );
              }).toList(),
              onChanged: (value) => selectedGroupId = value,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo(color: Colors.grey)),
          ),
          TextButton(
            onPressed: () {
              if (nameController.text.trim().isNotEmpty && selectedGroupId != null) {
                final group = provider.groups.firstWhere((g) => g.id == selectedGroupId);
                final updatedStudent = student.copyWith(
                  name: nameController.text.trim(),
                  groupId: selectedGroupId!,
                  phoneNumber: phoneController.text.trim().isEmpty ? null : phoneController.text.trim(),
                );
                provider.updateStudentInGroup(group, updatedStudent);
                Navigator.pop(context);
              }
            },
            child: Text('حفظ', style: GoogleFonts.cairo(color: SimpleTheme.primary)),
          ),
        ],
      ),
    );
  }

  void _showNotesDialog(BuildContext context, AppProvider provider, student) {
    final notesController = TextEditingController(text: student.notes ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.getCardColor(context),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          'ملاحظات الطالب',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        content: TextField(
          controller: notesController,
          maxLines: 4,
          style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
          decoration: InputDecoration(
            hintText: 'أضف ملاحظات عن الطالب...',
            hintStyle: GoogleFonts.cairo(color: SimpleTheme.getSecondaryTextColor(context)),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo(color: Colors.grey)),
          ),
          TextButton(
            onPressed: () {
              final group = provider.groups.firstWhere((g) => g.id == student.groupId);
              final updatedStudent = student.copyWith(
                notes: notesController.text.trim(),
              );
              provider.updateStudentInGroup(group, updatedStudent);
              Navigator.pop(context);
            },
            child: Text('حفظ', style: GoogleFonts.cairo(color: SimpleTheme.primary)),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    AppProvider provider,
    student,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.getCardColor(context),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          'حذف الطالب',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.red,
          ),
        ),
        content: Text(
          'هل أنت متأكد من حذف الطالب "${student.name}"؟\nسيتم حذف جميع بيانات الطالب نهائياً.',
          style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo(color: Colors.grey)),
          ),
          TextButton(
            onPressed: () {
              provider.deleteStudent(student.id);
              Navigator.pop(context);
            },
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentCard(student, group, AppProvider provider) {
    // حساب حالة التأخير
    final paymentProvider = context.read<StudentPaymentProvider>();
    final payment = paymentProvider.getStudentPayment(student.id);

    bool isOverdue = false;
    if (payment != null && !payment.isFullyPaid) {
      final settings = paymentProvider.settings;

      // حساب عدد الحصص التي حضرها الطالب
      final studentAttendedSessions = provider.lessons
          .where(
            (lesson) =>
                lesson.groupId == student.groupId &&
                lesson.attendedStudentIds.contains(student.id) &&
                lesson.isCompleted,
          )
          .length;

      isOverdue = payment.isOverdueWithNewSettings(
        overdueMethod: settings.overdueCalculationMethod,
        sessionsCount: settings.sessionsCount,
        graceDays: settings.overdueGraceDays,
        studentAttendedSessions: studentAttendedSessions,
      );
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isOverdue
              ? Colors.red.withValues(alpha: 0.3)
              : SimpleTheme.primary.withValues(alpha: 0.2),
          width: isOverdue ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isOverdue
                ? Colors.red.withValues(alpha: 0.1)
                : SimpleTheme.primary.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: SimpleTheme.primary.withValues(alpha: 0.1),
          child: Icon(Icons.person_rounded, color: SimpleTheme.primary),
        ),
        onTap: () => _showStudentDetails(context, student, group, provider),
        title: Text(
          student.name,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              'المجموعة: ${group?.name ?? 'غير محدد'}',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: SimpleTheme.getSecondaryTextColor(context),
              ),
            ),
            if (student.phoneNumber != null && student.phoneNumber!.isNotEmpty)
              Text(
                'الهاتف: ${student.phoneNumber}',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              ),
            if (student.notes != null && student.notes!.isNotEmpty)
              Text(
                'ملاحظات: ${student.notes}',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            // مؤشر حالة التأخير
            if (isOverdue)
              Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Text(
                  'متأخر في السداد',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: Icon(Icons.more_vert, color: Colors.grey.shade600),
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditStudentDialog(context, provider, student);
                break;
              case 'notes':
                _showNotesDialog(context, provider, student);
                break;
              case 'delete':
                _showDeleteConfirmation(context, provider, student);
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(
                    Icons.edit_rounded,
                    size: 18,
                    color: SimpleTheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text('تعديل', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'notes',
              child: Row(
                children: [
                  Icon(
                    Icons.note_add_rounded,
                    size: 18,
                    color: SimpleTheme.accent,
                  ),
                  const SizedBox(width: 8),
                  Text('ملاحظات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete_rounded, size: 18, color: Colors.red),
                  const SizedBox(width: 8),
                  Text('حذف', style: GoogleFonts.cairo()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  void _showStudentDetails(BuildContext context, student, group, AppProvider provider) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StudentDetailsScreen(
          student: student,
          group: group,
        ),
      ),
    );
  }
}

class StudentDetailsScreen extends StatelessWidget {
  final dynamic student;
  final dynamic group;

  const StudentDetailsScreen({
    super.key,
    required this.student,
    required this.group,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        final paymentProvider = context.read<StudentPaymentProvider>();
        final payment = paymentProvider.getStudentPayment(student.id);
        final attendanceLessons = provider.lessons
            .where((lesson) => lesson.groupId == student.groupId)
            .toList();

        return Scaffold(
          backgroundColor: SimpleTheme.getBackgroundColor(context),
          appBar: AppBar(
            title: Text(
              student.name,
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
                color: SimpleTheme.getTextColor(context),
              ),
            ),
            backgroundColor: SimpleTheme.getBackgroundColor(context),
            elevation: 0,
            leading: IconButton(
              icon: Icon(Icons.arrow_back_ios, color: SimpleTheme.getIconColor(context)),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStudentInfo(context),
                const SizedBox(height: 24),
                _buildAttendanceSection(context, attendanceLessons),
                const SizedBox(height: 24),
                _buildPaymentSection(context, payment, paymentProvider),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStudentInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: SimpleTheme.getBorderColor(context),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: SimpleTheme.primary.withValues(alpha: 0.1),
                child: Icon(Icons.person_rounded, color: SimpleTheme.primary, size: 30),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      student.name,
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                    Text(
                      'المجموعة: ${group?.name ?? 'غير محدد'}',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (student.phoneNumber != null && student.phoneNumber!.isNotEmpty)
            _buildInfoRow('الهاتف', student.phoneNumber!, Icons.phone),
          if (student.notes != null && student.notes!.isNotEmpty)
            _buildInfoRow('الملاحظات', student.notes!, Icons.note),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: SimpleTheme.primary),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.w600,
              color: SimpleTheme.getTextColor(context),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                color: SimpleTheme.getSecondaryTextColor(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceSection(BuildContext context, List attendanceLessons) {
    final attendedLessons = attendanceLessons.where((lesson) => 
        lesson.attendedStudentIds.contains(student.id)).length;
    final totalLessons = attendanceLessons.where((lesson) => lesson.isCompleted).length;
    final attendanceRate = totalLessons > 0 ? (attendedLessons / totalLessons * 100).round() : 0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: SimpleTheme.getBorderColor(context),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'سجل الحضور',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: SimpleTheme.getTextColor(context),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem('حضر', attendedLessons.toString(), Colors.green),
              ),
              Expanded(
                child: _buildStatItem('إجمالي', totalLessons.toString(), Colors.blue),
              ),
              Expanded(
                child: _buildStatItem('النسبة', '$attendanceRate%', Colors.orange),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSection(BuildContext context, payment, paymentProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: SimpleTheme.getBorderColor(context),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'سجل المدفوعات',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: SimpleTheme.getTextColor(context),
            ),
          ),
          const SizedBox(height: 16),
          if (payment != null) ...[
            Row(
              children: [
                Expanded(
                  child: _buildStatItem('المدفوع', '${payment.paidAmount}', Colors.green),
                ),
                Expanded(
                  child: _buildStatItem('المطلوب', '${payment.totalAmount}', Colors.blue),
                ),
                Expanded(
                  child: _buildStatItem('المتبقي', '${payment.remainingAmount}', Colors.red),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: payment.isFullyPaid 
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: payment.isFullyPaid 
                      ? Colors.green.withValues(alpha: 0.3)
                      : Colors.orange.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                payment.isFullyPaid ? 'مكتمل الدفع' : 'غير مكتمل الدفع',
                style: GoogleFonts.cairo(
                  color: payment.isFullyPaid ? Colors.green : Colors.orange,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ] else
            Text(
              'لا توجد بيانات دفع',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getSecondaryTextColor(context),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: SimpleTheme.getSecondaryTextColor(context),
          ),
        ),
      ],
    );
  }
}