import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../theme/simple_theme.dart';
import '../widgets/premium_card.dart';
import '../widgets/gradient_button.dart';
import '../models/group.dart';
import '../models/lesson.dart';

class ScheduleSettingsScreen extends StatefulWidget {
  const ScheduleSettingsScreen({super.key});

  @override
  State<ScheduleSettingsScreen> createState() => _ScheduleSettingsScreenState();
}

class _ScheduleSettingsScreenState extends State<ScheduleSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إعدادات الجدول',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: SimpleTheme.cardBg,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [SimpleTheme.darkBg, SimpleTheme.cardBg],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Consumer<AppProvider>(
            builder: (context, provider, child) {
              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'جدولة الدروس الأسبوعية',
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                    const SizedBox(height: 16),

                    if (provider.groups.isEmpty)
                      PremiumCard(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            'لا توجد مجموعات لجدولتها',
                            style: GoogleFonts.cairo(
                              color: SimpleTheme.getTextColor(
                                context,
                              ).withValues(alpha: 0.7),
                              fontSize: 14,
                            ),
                          ),
                        ),
                      )
                    else
                      ...provider.groups.map(
                        (group) => _GroupScheduleCard(group: group),
                      ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

class _GroupScheduleCard extends StatefulWidget {
  final Group group;

  const _GroupScheduleCard({required this.group});

  @override
  State<_GroupScheduleCard> createState() => _GroupScheduleCardState();
}

class _GroupScheduleCardState extends State<_GroupScheduleCard> {
  final List<String> days = [
    'الأحد',
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة',
    'السبت',
  ];

  Map<String, List<String>> schedule = {};

  @override
  void initState() {
    super.initState();
    schedule = Map.from(widget.group.schedule);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getContainerColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: SimpleTheme.getBorderColor(context)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.group.name,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: SimpleTheme.getTextColor(context),
            ),
          ),
          const SizedBox(height: 12),
          ...days.map((day) => _buildDaySchedule(day)),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: GradientButton(
              text: '${'حفظ'} ${'الجدول الزمني'}',
              onPressed: _saveScheduleAndGenerateLessons,
              height: 45,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDaySchedule(String day) {
    final dayTimes = schedule[day] ?? [];

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              day,
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context).withValues(alpha: 0.7),
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Wrap(
              spacing: 8,
              children: [
                ...dayTimes.map(
                  (time) => Chip(
                    label: Text(time, style: GoogleFonts.cairo(fontSize: 12)),
                    backgroundColor: const Color(
                      0xFF6366f1,
                    ).withValues(alpha: 0.2),
                    deleteIcon: const Icon(Icons.close, size: 16),
                    onDeleted: () {
                      setState(() {
                        dayTimes.remove(time);
                        if (dayTimes.isEmpty) {
                          schedule.remove(day);
                        }
                      });
                    },
                  ),
                ),
                GestureDetector(
                  onTap: () => _addTimeSlot(day),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: SimpleTheme.getTextColor(
                          context,
                        ).withValues(alpha: 0.3),
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      Icons.add,
                      color: SimpleTheme.getIconColor(
                        context,
                      ).withValues(alpha: 0.7),
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _addTimeSlot(String day) async {
    // اختيار وقت البداية
    final TimeOfDay? startTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      helpText: 'اختر وقت بداية الدرس',
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Color(0xFF6366f1),
              surface: Color(0xFF1e293b),
            ),
          ),
          child: child!,
        );
      },
    );

    if (startTime == null) return;

    if (!mounted) return;

    // اختيار وقت النهاية
    final TimeOfDay? endTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(
        hour: startTime.hour + 1,
        minute: startTime.minute,
      ),
      helpText: 'اختر وقت نهاية الدرس',
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Color(0xFF6366f1),
              surface: Color(0xFF1e293b),
            ),
          ),
          child: child!,
        );
      },
    );

    if (endTime == null || !mounted) return;

    // التحقق من أن وقت النهاية بعد وقت البداية
    final startMinutes = startTime.hour * 60 + startTime.minute;
    final endMinutes = endTime.hour * 60 + endTime.minute;

    if (endMinutes <= startMinutes) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'وقت النهاية يجب أن يكون بعد وقت البداية',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final startTimeString =
        '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}';
    final endTimeString =
        '${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}';
    final timeSlot = '$startTimeString - $endTimeString';

    setState(() {
      if (schedule[day] == null) {
        schedule[day] = [];
      }
      if (!schedule[day]!.contains(timeSlot)) {
        schedule[day]!.add(timeSlot);
        schedule[day]!.sort();
      }
    });
  }

  void _saveScheduleAndGenerateLessons() async {
    // حفظ الجدول أولاً
    widget.group.schedule = schedule;
    widget.group.save();

    // ثم إنشاء الدروس تلقائياً
    await _generateLessonsFromSchedule();
  }

  Future<void> _generateLessonsFromSchedule() async {
    final provider = Provider.of<AppProvider>(context, listen: false);
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday % 7));

    int lessonsCreated = 0;

    // Generate lessons for the next 4 weeks
    for (int week = 0; week < 4; week++) {
      for (String day in schedule.keys) {
        final dayIndex = days.indexOf(day);
        final lessonDate = startOfWeek.add(
          Duration(days: dayIndex + (week * 7)),
        );

        for (String timeSlot in schedule[day]!) {
          // تحليل الوقت الجديد (البداية - النهاية)
          DateTime? startDateTime;
          DateTime? endDateTime;

          if (timeSlot.contains(' - ')) {
            // تنسيق جديد: "09:00 - 10:00"
            final times = timeSlot.split(' - ');
            final startParts = times[0].split(':');
            final endParts = times[1].split(':');

            startDateTime = DateTime(
              lessonDate.year,
              lessonDate.month,
              lessonDate.day,
              int.parse(startParts[0]),
              int.parse(startParts[1]),
            );

            endDateTime = DateTime(
              lessonDate.year,
              lessonDate.month,
              lessonDate.day,
              int.parse(endParts[0]),
              int.parse(endParts[1]),
            );
          } else {
            // تنسيق قديم: "09:00" (للتوافق مع البيانات القديمة)
            final timeParts = timeSlot.split(':');
            startDateTime = DateTime(
              lessonDate.year,
              lessonDate.month,
              lessonDate.day,
              int.parse(timeParts[0]),
              int.parse(timeParts[1]),
            );
            // افتراض مدة ساعة واحدة للدروس القديمة
            endDateTime = startDateTime.add(const Duration(hours: 1));
          }

          // Only create future lessons
          if (startDateTime.isAfter(now)) {
            final lesson = Lesson(
              id: '${widget.group.id}_${startDateTime.millisecondsSinceEpoch}',
              groupId: widget.group.id,
              dateTime: startDateTime,
              endTime: endDateTime,
              subject: widget.group.subject,
            );

            await provider.addLesson(lesson);
            lessonsCreated++;
          }
        }
      }
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم بنجاح: $lessonsCreated حصة',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: const Color(0xFF10b981),
        ),
      );
    }
  }
}
