import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../theme/simple_theme.dart';
import '../providers/app_provider.dart';
import '../providers/student_payment_provider.dart';
import '../models/group.dart';
import '../models/student.dart';

import '../models/student_payment.dart';
import 'add_student_payment_screen.dart';
import 'payment_archive_screen.dart';
import 'payment_settings_screen.dart';

class GroupPaymentsScreen extends StatefulWidget {
  const GroupPaymentsScreen({super.key});

  @override
  State<GroupPaymentsScreen> createState() => _GroupPaymentsScreenState();
}

class _GroupPaymentsScreenState extends State<GroupPaymentsScreen> {
  Group? _selectedGroup;
  String _searchQuery = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: SimpleTheme.getBackgroundColor(context),
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            _buildGroupSelector(),
            if (_selectedGroup != null) ...[
              _buildSearchBar(),
              _buildStatistics(),
              Expanded(child: _buildStudentsList()),
            ] else
              Expanded(child: _buildEmptyState()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            SimpleTheme.primary.withValues(alpha: 0.1),
            SimpleTheme.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(
              Icons.arrow_back,
              color: SimpleTheme.getIconColor(context),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة المدفوعات',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: SimpleTheme.getTextColor(context),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'اختر المجموعة لإدارة مدفوعات الطلاب',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: SimpleTheme.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              // زر الأرشيف
              IconButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PaymentReportsScreen(),
                    ),
                  );
                },
                icon: Icon(Icons.analytics, color: Colors.blue),
                tooltip: 'تقارير المدفوعات',
              ),
              const SizedBox(width: 8),
              // زر الإعدادات
              IconButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PaymentSettingsScreen(),
                    ),
                  );
                },
                icon: Icon(
                  Icons.settings,
                  color: SimpleTheme.getIconColor(context),
                ),
                tooltip: 'إعدادات المدفوعات',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGroupSelector() {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: SimpleTheme.getBorderColor(context)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<Group>(
              value: _selectedGroup,
              hint: Text(
                'اختر المجموعة',
                style: GoogleFonts.cairo(
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              ),
              isExpanded: true,
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: SimpleTheme.getIconColor(context),
              ),
              items: provider.groups.map((group) {
                return DropdownMenuItem<Group>(
                  value: group,
                  child: Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: SimpleTheme.primary,
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              group.name,
                              style: GoogleFonts.cairo(
                                fontWeight: FontWeight.w600,
                                color: SimpleTheme.getTextColor(context),
                              ),
                            ),
                            Text(
                              group.subject,
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: SimpleTheme.getSecondaryTextColor(
                                  context,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        '${group.monthlyFee.toStringAsFixed(0)} ج.م',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: SimpleTheme.primary,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (Group? group) {
                setState(() {
                  _selectedGroup = group;
                  _searchQuery = '';
                });
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: TextField(
        onChanged: (value) => setState(() => _searchQuery = value),
        style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
        decoration: InputDecoration(
          hintText: 'البحث عن طالب...',
          hintStyle: GoogleFonts.cairo(
            color: SimpleTheme.getSecondaryTextColor(context),
          ),
          prefixIcon: Icon(
            Icons.search,
            color: SimpleTheme.getIconColor(context),
          ),
          filled: true,
          fillColor: SimpleTheme.getCardColor(context),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: SimpleTheme.getBorderColor(context)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: SimpleTheme.getBorderColor(context)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: SimpleTheme.primary, width: 2),
          ),
        ),
      ),
    );
  }

  Widget _buildStatistics() {
    return Consumer2<AppProvider, StudentPaymentProvider>(
      builder: (context, appProvider, paymentProvider, child) {
        if (_selectedGroup == null) return const SizedBox.shrink();

        final students = appProvider.getStudentsByGroup(_selectedGroup!.id);
        final paymentInfos = paymentProvider.getGroupPaymentInfo(
          _selectedGroup!.id,
          students,
          appProvider.lessons,
        );
        final totalPaid = paymentInfos.fold<double>(
          0,
          (sum, info) => sum + info.totalPaid,
        );
        final totalRemaining = paymentInfos.fold<double>(
          0,
          (sum, info) => sum + info.totalRemaining,
        );

        final overdueStudents = paymentInfos
            .where((info) => info.isOverdue)
            .length;

        return Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: SimpleTheme.getBorderColor(context)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'إحصائيات المجموعة',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: SimpleTheme.getTextColor(context),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'مدفوع',
                      '${totalPaid.toStringAsFixed(0)} ج.م',
                      Icons.check_circle,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatItem(
                      'متبقي',
                      '${totalRemaining.toStringAsFixed(0)} ج.م',
                      Icons.pending,
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatItem(
                      'متأخر',
                      '$overdueStudents طالب',
                      Icons.warning,
                      Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 10,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStudentsList() {
    return Consumer2<AppProvider, StudentPaymentProvider>(
      builder: (context, appProvider, paymentProvider, child) {
        if (_selectedGroup == null) return const SizedBox.shrink();

        var students = appProvider.getStudentsByGroup(_selectedGroup!.id);

        // تطبيق البحث
        if (_searchQuery.isNotEmpty) {
          students = students.where((student) {
            return student.name.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            );
          }).toList();
        }

        if (students.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.search_off,
                  size: 64,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
                const SizedBox(height: 16),
                Text(
                  _searchQuery.isNotEmpty
                      ? 'لا توجد نتائج للبحث'
                      : 'لا يوجد طلاب في هذه المجموعة',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: SimpleTheme.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: students.length,
          itemBuilder: (context, index) {
            final student = students[index];
            final payment = paymentProvider.getStudentPayment(student.id);

            return _buildStudentCard(student, payment);
          },
        );
      },
    );
  }

  Widget _buildStudentCard(Student student, StudentPayment? payment) {
    final paymentProvider = context.read<StudentPaymentProvider>();
    final appProvider = context.read<AppProvider>();
    final settings = paymentProvider.settings;

    // حساب عدد الحصص التي حضرها الطالب (باستثناء الملغية)
    final studentAttendedSessions = appProvider.lessons
        .where(
          (lesson) =>
              lesson.groupId == _selectedGroup?.id &&
              lesson.attendedStudentIds.contains(student.id) &&
              lesson.isCompleted &&
              !lesson.isCancelled,
        )
        .length;

    final isFullyPaid = payment?.isFullyPaid ?? false;
    
    // حساب التأخير
    bool isOverdue = false;
    if (payment != null && !payment.isFullyPaid) {
      isOverdue = payment.isOverdueWithNewSettings(
        overdueMethod: settings.overdueCalculationMethod,
        sessionsCount: settings.sessionsCount,
        graceDays: settings.overdueGraceDays,
        studentAttendedSessions: studentAttendedSessions,
      );
    } else if (payment == null && studentAttendedSessions > 0) {
      // إذا لم يوجد سجل دفعات ولديه حصص غير ملغية، فهو متأخر
      isOverdue = true;
    }

    final paidAmount = payment?.paidAmount ?? 0;
    final totalAmount = payment?.totalAmount ?? 
        (studentAttendedSessions > 0 ? (_selectedGroup?.monthlyFee ?? settings.defaultAmount) : 0.0);
    final progress = totalAmount > 0 ? paidAmount / totalAmount : 0.0;

    // تحديد اللون والنص
    Color statusColor;
    String statusText;
    IconData statusIcon;

    if (isFullyPaid) {
      statusColor = Colors.green;
      statusText = 'مدفوع';
      statusIcon = Icons.check_circle;
    } else if (isOverdue) {
      statusColor = Colors.red;
      statusText = 'متأخر';
      statusIcon = Icons.warning;
    } else if (totalAmount > 0) {
      statusColor = Colors.orange;
      statusText = 'جزئي';
      statusIcon = Icons.pending;
    } else {
      statusColor = Colors.grey;
      statusText = 'لا يوجد حصص';
      statusIcon = Icons.info;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToPaymentScreen(student),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: statusColor.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Icon(statusIcon, color: statusColor, size: 24),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          student.name,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: SimpleTheme.getTextColor(context),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              '${paidAmount.toStringAsFixed(0)} / ${totalAmount.toStringAsFixed(0)} ج.م',
                              style: GoogleFonts.cairo(
                                fontSize: 14,
                                color: SimpleTheme.getSecondaryTextColor(context),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '(حضر $studentAttendedSessions حصة)',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: SimpleTheme.getSecondaryTextColor(context),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      statusText,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: statusColor,
                      ),
                    ),
                  ),
                ],
              ),
              if (totalAmount > 0 && !isFullyPaid) ...[
                const SizedBox(height: 12),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.grey.withValues(alpha: 0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                ),
                const SizedBox(height: 8),
                Text(
                  'تم دفع ${(progress * 100).toStringAsFixed(0)}%',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: SimpleTheme.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.payment,
            size: 80,
            color: SimpleTheme.getSecondaryTextColor(context),
          ),
          const SizedBox(height: 24),
          Text(
            'اختر مجموعة لعرض الطلاب',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'يمكنك إدارة مدفوعات الطلاب من هنا',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToPaymentScreen(Student student) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            AddStudentPaymentScreen(student: student, group: _selectedGroup!),
      ),
    );
  }
}
