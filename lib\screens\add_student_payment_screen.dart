import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../theme/simple_theme.dart';
import '../providers/student_payment_provider.dart';
import '../models/student.dart';
import '../models/group.dart';
import '../models/student_payment.dart';

class AddStudentPaymentScreen extends StatefulWidget {
  final Student student;
  final Group group;

  const AddStudentPaymentScreen({
    super.key,
    required this.student,
    required this.group,
  });

  @override
  State<AddStudentPaymentScreen> createState() =>
      _AddStudentPaymentScreenState();
}

class _AddStudentPaymentScreenState extends State<AddStudentPaymentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isFullPayment = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadPaymentData();
  }

  void _loadPaymentData() {
    final paymentProvider = context.read<StudentPaymentProvider>();
    final payment = paymentProvider.getStudentPayment(widget.student.id);

    if (payment == null) {
      // إنشاء سجل جديد
      _amountController.text = widget.group.monthlyFee.toString();
    } else {
      // تحديث سجل موجود
      _amountController.text = payment.remainingAmount.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: SimpleTheme.getBackgroundColor(context),
      appBar: AppBar(
        backgroundColor: SimpleTheme.getBackgroundColor(context),
        title: Text(
          'إدارة مدفوعات ${widget.student.name}',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: SimpleTheme.getIconColor(context),
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Consumer<StudentPaymentProvider>(
        builder: (context, paymentProvider, child) {
          final payment = paymentProvider.getStudentPayment(widget.student.id);

          return SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStudentInfo(),
                const SizedBox(height: 24),
                if (payment != null) _buildPaymentHistory(payment),
                const SizedBox(height: 24),
                _buildPaymentForm(payment),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStudentInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            SimpleTheme.primary.withValues(alpha: 0.1),
            SimpleTheme.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: SimpleTheme.primary.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: SimpleTheme.primary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(Icons.person, color: SimpleTheme.primary, size: 30),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.student.name,
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: SimpleTheme.getTextColor(context),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.group.name,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: SimpleTheme.getSecondaryTextColor(context),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'الرسوم الشهرية: ${widget.group.monthlyFee.toStringAsFixed(0)} ج.م',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: SimpleTheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentHistory(StudentPayment payment) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: SimpleTheme.getBorderColor(context)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'حالة السداد',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: SimpleTheme.getTextColor(context),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatusItem(
                  'المبلغ الإجمالي',
                  '${payment.totalAmount.toStringAsFixed(0)} ج.م',
                  Icons.account_balance_wallet,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatusItem(
                  'المدفوع',
                  '${payment.paidAmount.toStringAsFixed(0)} ج.م',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatusItem(
                  'المتبقي',
                  '${payment.remainingAmount.toStringAsFixed(0)} ج.م',
                  Icons.pending,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: payment.paymentProgress,
            backgroundColor: Colors.grey.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              payment.isFullyPaid ? Colors.green : Colors.orange,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'تم دفع ${(payment.paymentProgress * 100).toStringAsFixed(0)}%',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
          ),
          if (payment.transactions.isNotEmpty) ...[
            const SizedBox(height: 20),
            Text(
              'المعاملات',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: SimpleTheme.getTextColor(context),
              ),
            ),
            const SizedBox(height: 12),
            ...payment.transactions.map((transaction) {
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.green.withValues(alpha: 0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.payment, color: Colors.green, size: 20),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${transaction.amount.toStringAsFixed(0)} ج.م',
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          Text(
                            '${transaction.date.day}/${transaction.date.month}/${transaction.date.year}',
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: SimpleTheme.getSecondaryTextColor(context),
                            ),
                          ),
                          if (transaction.notes != null)
                            Text(
                              transaction.notes!,
                              style: GoogleFonts.cairo(
                                fontSize: 11,
                                color: SimpleTheme.getSecondaryTextColor(context),
                              ),
                            ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => _deleteTransaction(payment, transaction.id),
                      icon: Icon(Icons.delete, color: Colors.red, size: 20),
                      tooltip: 'حذف المعاملة',
                    ),
                  ],
                ),
              );
            }),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 10,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentForm(StudentPayment? payment) {
    return Form(
      key: _formKey,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: SimpleTheme.getCardColor(context),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: SimpleTheme.getBorderColor(context)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إضافة دفعة جديدة',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: SimpleTheme.getTextColor(context),
              ),
            ),
            const SizedBox(height: 20),

            // نوع الدفعة
            Row(
              children: [
                Expanded(
                  child: RadioListTile<bool>(
                    title: Text(
                      'سداد كامل',
                      style: GoogleFonts.cairo(
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                    value: true,
                    groupValue: _isFullPayment,
                    onChanged: (value) {
                      setState(() {
                        _isFullPayment = value!;
                        if (_isFullPayment && payment != null) {
                          _amountController.text = payment.remainingAmount
                              .toString();
                        }
                      });
                    },
                    activeColor: SimpleTheme.primary,
                  ),
                ),
                Expanded(
                  child: RadioListTile<bool>(
                    title: Text(
                      'سداد جزئي',
                      style: GoogleFonts.cairo(
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                    value: false,
                    groupValue: _isFullPayment,
                    onChanged: (value) {
                      setState(() {
                        _isFullPayment = value!;
                        if (!_isFullPayment) {
                          _amountController.clear();
                        }
                      });
                    },
                    activeColor: SimpleTheme.primary,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // مبلغ الدفعة
            TextFormField(
              controller: _amountController,
              keyboardType: TextInputType.number,
              enabled: !_isFullPayment || payment == null,
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
              decoration: InputDecoration(
                labelText: 'مبلغ الدفعة',
                hintText: 'أدخل المبلغ',
                prefixIcon: Icon(
                  Icons.attach_money,
                  color: SimpleTheme.getIconColor(context),
                ),
                suffixText: 'ج.م',
                filled: true,
                fillColor: SimpleTheme.getCardColor(context),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: SimpleTheme.getBorderColor(context),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: SimpleTheme.getBorderColor(context),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: SimpleTheme.primary, width: 2),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال المبلغ';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'يرجى إدخال مبلغ صحيح';
                }
                if (payment != null && amount > payment.remainingAmount) {
                  return 'المبلغ أكبر من المتبقي';
                }
                return null;
              },
            ),

            const SizedBox(height: 20),

            // ملاحظات
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
              decoration: InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                hintText: 'أضف ملاحظات حول الدفعة',
                prefixIcon: Icon(
                  Icons.note,
                  color: SimpleTheme.getIconColor(context),
                ),
                filled: true,
                fillColor: SimpleTheme.getCardColor(context),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: SimpleTheme.getBorderColor(context),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: SimpleTheme.getBorderColor(context),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: SimpleTheme.primary, width: 2),
                ),
              ),
            ),

            const SizedBox(height: 30),

            // أزرار الحفظ
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _savePayment,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: SimpleTheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : Text(
                            'حفظ الدفعة',
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _savePayment() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final paymentProvider = context.read<StudentPaymentProvider>();
      final amount = double.parse(_amountController.text);
      final notes = _notesController.text.trim();

      // التحقق من وجود سجل دفعات
      final existingPayment = paymentProvider.getStudentPayment(
        widget.student.id,
      );

      if (existingPayment == null) {
        // إنشاء سجل جديد
        await paymentProvider.createStudentPayment(
          studentId: widget.student.id,
          studentName: widget.student.name,
          groupId: widget.group.id,
          groupName: widget.group.name,
          totalAmount: widget.group.monthlyFee > 0
              ? widget.group.monthlyFee
              : amount,
          notes: 'سجل جديد',
        );

        // إضافة الدفعة
        await paymentProvider.addPaymentToStudent(
          studentId: widget.student.id,
          amount: amount,
          notes: notes.isNotEmpty ? notes : null,
        );
      } else {
        // إضافة دفعة لسجل موجود
        await paymentProvider.addPaymentToStudent(
          studentId: widget.student.id,
          amount: amount,
          notes: notes.isNotEmpty ? notes : null,
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حفظ الدفعة بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: Colors.green,
          ),
        );

        // مسح النموذج
        _amountController.clear();
        _notesController.clear();
        setState(() => _isFullPayment = true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ: ${e.toString()}',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _deleteTransaction(StudentPayment payment, String transactionId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.getCardColor(context),
        title: Text(
          'حذف المعاملة',
          style: GoogleFonts.cairo(
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        content: Text(
          'هل أنت متأكد من حذف هذه المعاملة؟',
          style: GoogleFonts.cairo(
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        final paymentProvider = context.read<StudentPaymentProvider>();
        await paymentProvider.removeTransaction(
          studentId: widget.student.id,
          transactionId: transactionId,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف المعاملة بنجاح', style: GoogleFonts.cairo()),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ: ${e.toString()}', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
