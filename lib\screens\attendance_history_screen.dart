import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';
import '../models/group.dart';
import '../theme/simple_theme.dart';

class AttendanceHistoryScreen extends StatefulWidget {
  const AttendanceHistoryScreen({super.key});

  @override
  State<AttendanceHistoryScreen> createState() =>
      _AttendanceHistoryScreenState();
}

class _AttendanceHistoryScreenState extends State<AttendanceHistoryScreen> {
  // دالة لحساب نسبة الحضور
  String _calculatePercentage(int present, int total) {
    if (total == 0) return '0%';
    final percentage = (present / total * 100).round();
    return '$percentage%';
  }

  Group? selectedGroup;
  DateTime selectedMonth = DateTime.now();
  List<DateTime> scheduleDates = [];
  Map<String, Map<String, bool>> attendanceData = {};

  // تحويل رقم اليوم إلى اسم اليوم بالعربية
  final Map<int, String> weekdayNames = {
    DateTime.saturday: 'السبت',
    DateTime.sunday: 'الأحد',
    DateTime.monday: 'الإثنين',
    DateTime.tuesday: 'الثلاثاء',
    DateTime.wednesday: 'الأربعاء',
    DateTime.thursday: 'الخميس',
    DateTime.friday: 'الجمعة',
  };

  // أسماء الأشهر بالعربية
  final List<String> monthNames = [
    'يناير',
    'فبراير',
    'مارس',
    'أبريل',
    'مايو',
    'يونيو',
    'يوليو',
    'أغسطس',
    'سبتمبر',
    'أكتوبر',
    'نوفمبر',
    'ديسمبر',
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        final groups = provider.groups;

        return Scaffold(
          backgroundColor: SimpleTheme.getBackgroundColor(context),
          appBar: AppBar(
            backgroundColor: SimpleTheme.getBackgroundColor(context),
            title: Text(
              'سجل الحضور',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: SimpleTheme.getTextColor(context),
              ),
            ),
            centerTitle: true,
          ),
          body: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // اختيار المجموعة
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: SimpleTheme.getContainerColor(context),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: DropdownButton<Group>(
                    isExpanded: true,
                    dropdownColor: const Color(0xFF1e293b),
                    underline: const SizedBox(),
                    hint: Text(
                      'اختر المجموعة',
                      style: GoogleFonts.cairo(
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                    ),
                    value: selectedGroup,
                    items: groups.map((group) {
                      return DropdownMenuItem<Group>(
                        value: group,
                        child: Text(
                          group.name,
                          style: GoogleFonts.cairo(
                            color: SimpleTheme.getTextColor(context),
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedGroup = value;
                        _initAttendanceData(provider);
                      });
                    },
                  ),
                ),

                const SizedBox(height: 16),

                // اختيار الشهر
                if (selectedGroup != null) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        icon: Icon(
                          Icons.arrow_back_ios,
                          color: SimpleTheme.getIconColor(context),
                        ),
                        onPressed: () {
                          setState(() {
                            selectedMonth = DateTime(
                              selectedMonth.year,
                              selectedMonth.month - 1,
                              1,
                            );
                            _initAttendanceData(provider);
                          });
                        },
                      ),
                      Text(
                        '${monthNames[selectedMonth.month - 1]} ${selectedMonth.year}',
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: SimpleTheme.getTextColor(context),
                        ),
                      ),
                      IconButton(
                        icon: Icon(
                          Icons.arrow_forward_ios,
                          color: SimpleTheme.getIconColor(context),
                        ),
                        onPressed: () {
                          final now = DateTime.now();
                          // لا نسمح باختيار شهر في المستقبل
                          if (selectedMonth.year < now.year ||
                              (selectedMonth.year == now.year &&
                                  selectedMonth.month < now.month)) {
                            setState(() {
                              selectedMonth = DateTime(
                                selectedMonth.year,
                                selectedMonth.month + 1,
                                1,
                              );
                              _initAttendanceData(provider);
                            });
                          }
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // جدول الحضور
                  Expanded(child: _buildAttendanceTable(provider)),
                ] else ...[
                  Expanded(
                    child: Center(
                      child: Text(
                        'الرجاء اختيار مجموعة لعرض سجل الحضور',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          color: SimpleTheme.getSecondaryTextColor(context),
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  void _initAttendanceData(AppProvider provider) {
    if (selectedGroup == null) return;

    final students = provider.getStudentsByGroup(selectedGroup!.id);
    final now = DateTime.now();
    final groupLessons = provider.lessons
        .where(
          (lesson) =>
              lesson.groupId == selectedGroup!.id &&
              lesson.dateTime.year == selectedMonth.year &&
              lesson.dateTime.month == selectedMonth.month &&
              lesson.isCompleted &&
              !lesson.isCancelled &&
              lesson.dateTime.isBefore(now.add(const Duration(days: 1))), // حتى اليوم الحالي فقط
        )
        .toList();

    // استخراج التواريخ الفريدة من جدول المواعيد للشهر المحدد
    scheduleDates = [];
    for (final lesson in groupLessons) {
      final lessonDate = DateTime(
        lesson.dateTime.year,
        lesson.dateTime.month,
        lesson.dateTime.day,
      );
      if (!scheduleDates.any(
        (date) =>
            date.year == lessonDate.year &&
            date.month == lessonDate.month &&
            date.day == lessonDate.day,
      )) {
        scheduleDates.add(lessonDate);
      }
    }

    // ترتيب التواريخ
    scheduleDates.sort((a, b) => a.compareTo(b));

    // تهيئة بيانات الحضور
    attendanceData.clear();
    for (final student in students) {
      attendanceData[student.id] = {};
      for (final date in scheduleDates) {
        // استخدام التاريخ كمفتاح بتنسيق سنة-شهر-يوم
        final dateKey = '${date.year}-${date.month}-${date.day}';
        attendanceData[student.id]![dateKey] = false;

        // التحقق من الحضور السابق
        for (final lesson in groupLessons) {
          if (lesson.dateTime.year == date.year &&
              lesson.dateTime.month == date.month &&
              lesson.dateTime.day == date.day &&
              lesson.attendedStudentIds.contains(student.id)) {
            attendanceData[student.id]![dateKey] = true;
            break;
          }
        }
      }
    }
  }

  Widget _buildAttendanceTable(AppProvider provider) {
    if (selectedGroup == null) return const SizedBox();

    final students = provider.getStudentsByGroup(selectedGroup!.id);
    if (students.isEmpty) {
      return Center(
        child: Text(
          'لا يوجد طلاب في هذه المجموعة',
          style: GoogleFonts.cairo(
            fontSize: 16,
            color: SimpleTheme.getSecondaryTextColor(context),
          ),
        ),
      );
    }

    // تحقق من وجود دروس مكتملة غير ملغية حتى اليوم الحالي
    final now = DateTime.now();
    final completedLessons = provider.lessons
        .where(
          (lesson) =>
              lesson.groupId == selectedGroup!.id &&
              lesson.dateTime.year == selectedMonth.year &&
              lesson.dateTime.month == selectedMonth.month &&
              lesson.attendedStudentIds.isNotEmpty &&
              !lesson.isCancelled &&
              lesson.dateTime.isBefore(now.add(const Duration(days: 1))), // حتى اليوم الحالي فقط
        )
        .toList();

    if (scheduleDates.isEmpty || completedLessons.isEmpty) {
      return Center(
        child: Text(
          'لا توجد سجلات حضور لهذا الشهر',
          style: GoogleFonts.cairo(
            fontSize: 16,
            color: SimpleTheme.getSecondaryTextColor(context),
          ),
        ),
      );
    }

    // حساب إحصائيات الحضور والغياب لكل طالب
    Map<String, Map<String, int>> attendanceStats = {};
    for (final student in students) {
      int present = 0;
      int absent = 0;

      for (final date in scheduleDates) {
        final dateKey = '${date.year}-${date.month}-${date.day}';
        // فقط عد الحصص غير الملغية
        final lesson = provider.lessons.firstWhere(
          (l) => l.groupId == selectedGroup!.id &&
                 l.dateTime.year == date.year &&
                 l.dateTime.month == date.month &&
                 l.dateTime.day == date.day,
          orElse: () => provider.lessons.first,
        );
        
        if (!lesson.isCancelled) {
          if (attendanceData[student.id]?[dateKey] == true) {
            present++;
          } else {
            absent++;
          }
        }
      }

      attendanceStats[student.id] = {
        'present': present,
        'absent': absent,
        'total': present + absent,
      };
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          headingRowColor: WidgetStateProperty.all(
            SimpleTheme.getBorderColor(context),
          ),
          dataRowColor: WidgetStateProperty.all(Colors.transparent),
          border: TableBorder.all(
            color: SimpleTheme.getBorderColor(context),
            borderRadius: BorderRadius.circular(8),
          ),
          columns: [
            DataColumn(
              label: Text(
                'الطالب',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                  color: SimpleTheme.getTextColor(context),
                ),
              ),
            ),
            ...scheduleDates.map(
              (date) => DataColumn(
                label: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      weekdayNames[date.weekday] ?? '',
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      '${date.day}/${date.month}/${date.year}',
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // إضافة أعمدة إحصائيات الحضور والغياب
            DataColumn(
              label: Text(
                'حضور',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ),
            DataColumn(
              label: Text(
                'غياب',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ),
            DataColumn(
              label: Text(
                'نسبة الحضور',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                  color: SimpleTheme.getTextColor(context),
                ),
              ),
            ),
          ],
          rows: students.map((student) {
            return DataRow(
              cells: [
                DataCell(
                  Text(
                    student.name,
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                    ),
                  ),
                ),
                ...scheduleDates.map((date) {
                  final dateKey = '${date.year}-${date.month}-${date.day}';
                  final isPresent =
                      attendanceData[student.id]?[dateKey] ?? false;
                  return DataCell(
                    Icon(
                      isPresent ? Icons.check_circle : Icons.cancel,
                      color: isPresent
                          ? Colors.green
                          : Colors.red.withValues(alpha: 0.7),
                      size: 20,
                    ),
                  );
                }),
                // خلايا إحصائيات الحضور والغياب
                DataCell(
                  Text(
                    '${attendanceStats[student.id]?['present'] ?? 0}',
                    style: GoogleFonts.cairo(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    '${attendanceStats[student.id]?['absent'] ?? 0}',
                    style: GoogleFonts.cairo(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    _calculatePercentage(
                      attendanceStats[student.id]?['present'] ?? 0,
                      attendanceStats[student.id]?['total'] ?? 0,
                    ),
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }
}
