import 'package:hive/hive.dart';
import 'payment_settings.dart';

part 'student_payment.g.dart';

/// نموذج السداد المبسط - يدعم السداد الجزئي
@HiveType(typeId: 20)
class StudentPayment extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String studentId;

  @HiveField(2)
  String studentName;

  @HiveField(3)
  String groupId;

  @HiveField(4)
  String groupName;

  @HiveField(5)
  double totalAmount; // المبلغ الإجمالي المطلوب

  @HiveField(6)
  double paidAmount; // المبلغ المدفوع

  @HiveField(7)
  DateTime createdAt;

  @HiveField(8)
  DateTime lastPaymentDate; // تاريخ آخر دفعة

  @HiveField(9)
  List<PaymentTransaction> transactions; // قائمة المعاملات

  @HiveField(10)
  String? notes;

  StudentPayment({
    required this.id,
    required this.studentId,
    required this.studentName,
    required this.groupId,
    required this.groupName,
    required this.totalAmount,
    this.paidAmount = 0.0,
    required this.createdAt,
    DateTime? lastPaymentDate,
    List<PaymentTransaction>? transactions,
    this.notes,
  }) : lastPaymentDate = lastPaymentDate ?? createdAt,
       transactions = transactions ?? [];

  /// المبلغ المتبقي
  double get remainingAmount => totalAmount - paidAmount;

  /// هل تم السداد بالكامل
  bool get isFullyPaid => remainingAmount <= 0;

  /// هل الطالب متأخر في السداد (النظام الجديد)
  bool isOverdueWithNewSettings({
    required OverdueCalculationMethod overdueMethod,
    required int sessionsCount,
    required int graceDays,
    int? studentAttendedSessions,
    DateTime? firstLessonDate,
    DateTime? lastLessonDate,
  }) {
    if (isFullyPaid) return false;

    final now = DateTime.now();
    DateTime overdueDate;

    switch (overdueMethod) {
      case OverdueCalculationMethod.monthStart:
        // عند بداية أول حصة في الشهر
        final firstLesson = firstLessonDate ?? createdAt;
        overdueDate = firstLesson.add(Duration(days: graceDays));
        break;

      case OverdueCalculationMethod.monthEnd:
        // عند انتهاء آخر حصة في الشهر
        final lastLesson = lastLessonDate ?? createdAt;
        overdueDate = lastLesson.add(Duration(days: graceDays));
        break;

      case OverdueCalculationMethod.sessionsCount:
        // عند إكمال عدد محدد من الحصص
        if (studentAttendedSessions != null &&
            studentAttendedSessions >= sessionsCount) {
          final lastLesson = lastLessonDate ?? createdAt;
          overdueDate = lastLesson.add(Duration(days: graceDays));
          return now.isAfter(overdueDate);
        } else {
          return false;
        }
    }

    return now.isAfter(overdueDate);
  }

  /// هل الطالب متأخر في السداد (للتوافق مع الكود القديم)
  bool isOverdueWithSettings({
    required PaymentCalculationMethod calculationMethod,
    required int sessionsCount,
    required int graceDays,
    DateTime? lastLessonDate,
  }) {
    if (isFullyPaid) return false;

    final now = DateTime.now();
    DateTime overdueDate;

    // النظام الشهري فقط
    switch (calculationMethod) {
      case PaymentCalculationMethod.monthly:
        // حساب التأخير شهرياً - عند انتهاء الشهر
        final endOfMonth = DateTime(createdAt.year, createdAt.month + 1, 0);
        overdueDate = endOfMonth.add(Duration(days: graceDays + 1));
        break;
    }

    return now.isAfter(overdueDate);
  }

  /// هل الطالب متأخر (للتوافق مع الكود القديم)
  /// هذه الدالة تستخدم قيم افتراضية
  bool get isOverdue {
    if (isFullyPaid) return false;
    
    final now = DateTime.now();
    // حساب التأخير بناءً على تاريخ إنشاء السجل + شهر + أيام السماح
    final endOfMonth = DateTime(createdAt.year, createdAt.month + 1, 0);
    final overdueDate = endOfMonth.add(const Duration(days: 4)); // 3 أيام سماح + 1
    
    return now.isAfter(overdueDate);
  }

  /// حالة السداد
  PaymentStatus get status {
    if (isFullyPaid) return PaymentStatus.paid;
    if (isOverdue) return PaymentStatus.overdue;
    return PaymentStatus.pending; // حالة انتظار للطلاب الذين لم يتأخروا بعد
  }

  /// نسبة السداد (من 0 إلى 1)
  double get paymentProgress => totalAmount > 0 ? paidAmount / totalAmount : 0;

  /// إضافة معاملة دفع جديدة
  void addPayment(double amount, {String? notes}) {
    final transaction = PaymentTransaction(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      amount: amount,
      date: DateTime.now(),
      notes: notes,
    );

    transactions.add(transaction);
    paidAmount += amount;
    lastPaymentDate = DateTime.now();

    // التأكد من عدم تجاوز المبلغ المدفوع للمبلغ الإجمالي
    if (paidAmount > totalAmount) {
      paidAmount = totalAmount;
    }
  }

  /// حذف معاملة دفع
  void removeTransaction(String transactionId) {
    final transaction = transactions.firstWhere(
      (t) => t.id == transactionId,
      orElse: () => throw Exception('Transaction not found'),
    );

    transactions.removeWhere((t) => t.id == transactionId);
    paidAmount -= transaction.amount;

    if (paidAmount < 0) paidAmount = 0;

    // تحديث تاريخ آخر دفعة
    if (transactions.isNotEmpty) {
      transactions.sort((a, b) => b.date.compareTo(a.date));
      lastPaymentDate = transactions.first.date;
    } else {
      lastPaymentDate = createdAt;
    }
  }

  /// الحصول على آخر معاملة
  PaymentTransaction? get lastTransaction {
    if (transactions.isEmpty) return null;
    transactions.sort((a, b) => b.date.compareTo(a.date));
    return transactions.first;
  }
}

/// معاملة دفع فردية
@HiveType(typeId: 21)
class PaymentTransaction extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  double amount;

  @HiveField(2)
  DateTime date;

  @HiveField(3)
  String? notes;

  PaymentTransaction({
    required this.id,
    required this.amount,
    required this.date,
    this.notes,
  });
}

/// حالة السداد
@HiveType(typeId: 22)
enum PaymentStatus {
  @HiveField(0)
  paid, // مدفوع

  @HiveField(1)
  overdue, // متأخر

  @HiveField(2)
  pending, // في الانتظار
}

/// إحصائيات المدفوعات المحسنة
class PaymentStatistics {
  final double totalPaid;
  final double totalOverdue;
  final int totalStudents;
  final int paidStudents;
  final int overdueStudents;
  final Map<String, double> revenueByGroup;

  PaymentStatistics({
    required this.totalPaid,
    required this.totalOverdue,
    required this.totalStudents,
    required this.paidStudents,
    required this.overdueStudents,
    required this.revenueByGroup,
  });

  /// معدل التحصيل
  double get collectionRate {
    final total = totalPaid + totalOverdue;
    if (total == 0) return 0;
    return (totalPaid / total) * 100;
  }
}

/// معلومات دفعات الطالب
class StudentPaymentInfo {
  final String studentId;
  final String studentName;
  final String groupName;
  final StudentPayment? payment;
  final double totalPaid;
  final double totalRemaining;
  final bool isOverdue;

  StudentPaymentInfo({
    required this.studentId,
    required this.studentName,
    required this.groupName,
    this.payment,
    required this.totalPaid,
    required this.totalRemaining,
    required this.isOverdue,
  });

  /// هل الطالب مدفوع بالكامل
  bool get isFullyPaid => totalRemaining <= 0;

  /// نسبة السداد
  double get paymentProgress => payment?.paymentProgress ?? 0;
}
